<script>
	import "../app.css"; // Import global styles, including Tailwind
	import "$lib/styles/amqplus.css"; // Import AMQ PLUS specific styles
	import Navbar from "$lib/components/amqplus/Navbar.svelte";
	import Footer from '$lib/components/amqplus/Footer.svelte';
</script>

<div class="relative min-h-screen overflow-hidden bg-gradient-to-br from-rose-50 to-pink-50">
	<!-- Light mode gradient blobs with AMQ PLUS colors -->


	<div class="relative z-10">
		<Navbar />
		<slot />
		<Footer />
	</div>
</div>

<style>
	/* Override the dark theme for AMQ PLUS pages */
	:global(body) {
		background-color: white;
		color: rgb(17 24 39); /* gray-900 */
	}
</style>
