<script>
	import { Button } from '$lib/components/ui/button';
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle
	} from '$lib/components/ui/dialog';

	let {
		open = $bindable(false),
		title = 'Confirm Action',
		description = 'Are you sure you want to proceed?',
		confirmText = 'Confirm',
		cancelText = 'Cancel',
		variant = 'destructive', // 'destructive' | 'default'
		onConfirm = () => {},
		onCancel = () => {}
	} = $props();

	function handleConfirm() {
		onConfirm();
		open = false;
	}

	function handleCancel() {
		onCancel();
		open = false;
	}
</script>

<Dialog bind:open>
	<DialogContent class="sm:max-w-md bg-white border shadow-lg">
		<DialogHeader>
			<DialogTitle class="flex items-center gap-2 text-gray-900">
				{#if variant === 'destructive'}
					<span class="text-red-500">⚠️</span>
				{:else}
					<span class="text-blue-500">ℹ️</span>
				{/if}
				{title}
			</DialogTitle>
			<DialogDescription class="text-gray-600 whitespace-pre-line">
				{description}
			</DialogDescription>
		</DialogHeader>
		<DialogFooter class="flex gap-2 sm:gap-2">
			<Button variant="outline" onclick={handleCancel} class="cursor-pointer flex-1">
				{cancelText}
			</Button>
			<Button
				variant={variant}
				onclick={handleConfirm}
				class="cursor-pointer flex-1 {variant === 'destructive' ? 'bg-red-500 hover:bg-red-600' : ''}"
			>
				{confirmText}
			</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>
