// settingsConfig.js - Configuration for all setting types

export const settingConfigs = {
	// Mode Zone Settings
	'scoring': {
		type: 'select',
		label: 'Scoring Method',
		size: 'small',
		options: [
			{ value: 'count', label: 'Count' },
			{ value: 'hint', label: 'Hint' },
			{ value: 'speed', label: 'Speed' }
		],
		default: 'count'
	},
	'answering': {
		type: 'select',
		label: 'Answering Method',
		size: 'small',
		options: [
			{ value: 'typing', label: 'Typing' },
			{ value: 'mix', label: 'Mix' },
			{ value: 'multiple-choice', label: 'Multiple Choice' }
		],
		default: 'typing'
	},

	// General Zone Settings
	'players': {
		type: 'number',
		label: 'Number of Players',
		size: 'small',
		min: 1,
		max: 100,
		default: 8
	},
	'team-size': {
		type: 'number',
		label: 'Team Size',
		size: 'small',
		min: 1,
		max: 8,
		default: 1
	},
	'songs-and-types': {
		type: 'complex-songs-and-types',
		label: 'Songs & Types Selection',
		size: 'large',
		default: {
			songCount: { random: false, value: 20, min: 15, max: 25 },
			songTypes: {
				openings: { enabled: true, percentage: 50, count: 10, random: false, percentageMin: 40, percentageMax: 60, countMin: 8, countMax: 12 },
				endings: { enabled: true, percentage: 50, count: 10, random: false, percentageMin: 40, percentageMax: 60, countMin: 8, countMax: 12 },
				inserts: { enabled: false, percentage: 0, count: 0, random: false, percentageMin: 0, percentageMax: 0, countMin: 0, countMax: 0 }
			},
			songSelection: {
				random: { value: 0, randomRange: false, min: 0, max: 10 },
				watched: { value: 100, randomRange: false, min: 90, max: 100 }
			},
			mode: 'percentage'
		}
	},
	'watched-distribution': {
		type: 'select',
		label: 'Watched Distribution',
		size: 'small',
		options: [
			{ value: 'random', label: 'Random' },
			{ value: 'equal', label: 'Equal' }
		],
		default: 'random'
	},

	// Quiz Zone Settings
	'guess-time': {
		type: 'number-with-random',
		label: 'Guess Time (seconds)',
		size: 'medium',
		min: 1,
		max: 60,
		default: { random: false, value: 20, min: 15, max: 25 },
		allowRandom: true
	},
	'extra-time': {
		type: 'number-with-random',
		label: 'Extra Guess Time (seconds)',
		size: 'medium',
		min: 0,
		max: 15,
		default: { random: false, value: 0, min: 0, max: 5 },
		allowRandom: true
	},
	'sample-point': {
		type: 'sample-point-with-static',
		label: 'Sample Point (%)',
		size: 'large',
		min: 1,
		max: 100,
		default: { useRange: true, start: 1, end: 100, staticValue: 50 }
	},
	'playback-speed': {
		type: 'select-with-random',
		label: 'Playback Speed',
		size: 'medium',
		options: [
			{ value: 1, label: '1x' },
			{ value: 1.5, label: '1.5x' },
			{ value: 2, label: '2x' },
			{ value: 4, label: '4x' }
		],
		default: { random: false, value: 1, options: { 1: true, 1.5: false, 2: false, 4: false } },
		allowRandom: true
	},
	'modifiers': {
		type: 'checkboxes',
		label: 'Modifiers',
		size: 'small',
		options: [
			{ key: 'skipGuessing', label: 'Skip Guessing', default: true },
			{ key: 'skipResults', label: 'Skip Results', default: true },
			{ key: 'queueing', label: 'Queueing', default: true }
		]
	},

	// Anime Zone Settings
	'anime-type': {
		type: 'checkboxes',
		label: 'Anime Types',
		size: 'small',
		options: [
			{ key: 'tv', label: 'TV', default: true },
			{ key: 'movie', label: 'Movie', default: true },
			{ key: 'ova', label: 'OVA', default: true },
			{ key: 'ona', label: 'ONA', default: true },
			{ key: 'special', label: 'Special', default: true }
		]
	},
	

	'song-difficulty': {
		type: 'complex-song-difficulty',
		label: 'Song Difficulty',
		size: 'large',
		default: {
			easy: { enabled: true, percentage: 33, count: 7, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 5, maxCount: 10 },
			medium: { enabled: true, percentage: 33, count: 7, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 5, maxCount: 10 },
			hard: { enabled: true, percentage: 34, count: 6, randomRange: false, minPercentage: 25, maxPercentage: 40, minCount: 4, maxCount: 8 },
			mode: 'percentage',
			viewMode: 'basic',
			ranges: []
		}
	},
	'player-score': {
		type: 'complex-score-range',
		label: 'Player Score',
		size: 'fullscreen',
		min: 1,
		max: 10,
		default: { min: 1, max: 10, mode: 'range', percentages: {} }
	},
	'anime-score': {
		type: 'complex-score-range',
		label: 'Anime Score',
		size: 'fullscreen',
		min: 2,
		max: 10,
		default: { min: 2, max: 10, mode: 'range', percentages: {} }
	},
	'vintage': {
		type: 'complex-vintage',
		label: 'Vintage',
		size: 'fullscreen',
		default: { ranges: [{ from: { season: 'Winter', year: 1944 }, to: { season: 'Fall', year: 2025 } }] }
	},
	'genres': {
		type: 'complex-genres-tags',
		label: 'Genres',
		size: 'fullscreen',
		default: { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} }
	},
	'tags': {
		type: 'complex-genres-tags',
		label: 'Tags',
		size: 'fullscreen',
		default: { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} }
	}
};
