<script>
	import { Input } from '$lib/components/ui/input/index.js';
	import * as Select from '$lib/components/ui/select/index.js';
	import RangeSlider from 'svelte-range-slider-pips';
	import { createEventDispatcher, onDestroy } from 'svelte';
	import { openModal, closeModal, openModals } from '$lib/stores/modalStore.js';

	let {
		value = $bindable(),
		type = 'text',
		options = [],
		placeholder = '',
		min,
		max,
		isEditing = $bindable(false),
		class: className = ''
	} = $props();

	const dispatch = createEventDispatcher();
	let tempValue = $state(value);
	let inputElement = $state(null);
	let selectOpen = $state(false);

	// Generate unique ID for this component based on a more stable identifier
	// Use crypto.randomUUID if available, otherwise fall back to Math.random
	const componentId = `inline-edit-${typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substr(2, 9)}`;

	// Watch for value changes from parent
	$effect(() => {
		tempValue = value;
	});

	// Focus input when editing starts
	$effect(() => {
		if (isEditing && inputElement && typeof inputElement.focus === 'function') {
			inputElement.focus();
			if (type === 'number' && typeof inputElement.select === 'function') {
				inputElement.select();
			}
		}
	});

	// Auto-open select dropdown when editing starts
	$effect(() => {
		if (isEditing && type === 'select') {
			// Small delay to ensure the select is rendered
			setTimeout(() => {
				selectOpen = true;
			}, 10);
		}
	});

	function startEdit() {
		// Close other open modals/dropdowns first
		openModal(componentId);
		tempValue = value;
		isEditing = true;
	}

	function saveEdit() {
		console.log('InlineEdit: saveEdit called with tempValue:', tempValue);

		// Validate number inputs
		if (type === 'number') {
			const numValue = Number(tempValue);
			if (min !== undefined && numValue < min) {
				tempValue = min;
			} else if (max !== undefined && numValue > max) {
				tempValue = max;
			}
		}

		value = tempValue;
		isEditing = false;
		selectOpen = false;
		closeModal(componentId);

		console.log('InlineEdit: dispatching save event with value:', tempValue);
		dispatch('save', { value: tempValue });
	}

	function cancelEdit() {
		tempValue = value;
		isEditing = false;
		selectOpen = false;
		closeModal(componentId);
	}

	function handleKeydown(event) {
		if (event.key === 'Enter') {
			saveEdit();
		} else if (event.key === 'Escape') {
			cancelEdit();
		}
	}

	function handleBlur() {
		// Don't save on blur if select is open
		if (type === 'select' && selectOpen) {
			return;
		}
		saveEdit();
	}

	function handleSelectValueChange(newValue) {
		tempValue = newValue;
		// Auto-save when selection is made
		setTimeout(() => {
			saveEdit();
		}, 0);
	}

	function handleSliderChange(event) {
		tempValue = event.detail.value;
		// Auto-save when slider changes
		setTimeout(() => {
			saveEdit();
		}, 100); // Small delay to avoid too frequent saves
	}

	function formatDisplayValue(val) {
		if (type === 'select' && options.length > 0) {
			const option = options.find(opt => opt.value === val);
			return option ? option.label : val;
		} else if (type === 'slider') {
			return `${val}%`;
		}
		return val || placeholder;
	}

	// Add global click listener when editing
	$effect(() => {
		if (isEditing) {
			const handleGlobalClick = (event) => {
				// Check if click is outside the inline edit container
				const container = event.target.closest('.inline-edit-container');
				const selectContent = event.target.closest('[data-slot="select-content"]');

				if (!container && !selectContent) {
					if (type === 'select' && selectOpen) {
						return; // Don't close if select dropdown is open
					}
					saveEdit();
				}
			};

			// Use capture phase to handle clicks before they bubble
			document.addEventListener('click', handleGlobalClick, true);
			return () => {
				document.removeEventListener('click', handleGlobalClick, true);
			};
		}
	});

	// Listen for other modals opening and close this one
	$effect(() => {
		const unsubscribe = openModals.subscribe(modals => {
			if (isEditing && modals.size > 0 && !modals.has(componentId)) {
				// Another modal opened, close this one
				cancelEdit();
			}
		});

		return unsubscribe;
	});

// Clean up when component is destroyed
onDestroy(() => {
	if (isEditing) {
		closeModal(componentId);
	}
});
</script>

<div class="inline-edit-container">
	{#if isEditing}
		{#if type === 'select'}
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<!-- svelte-ignore a11y_no_static_element_interactions -->
			<div onclick={(e) => e.stopPropagation()}>
				<Select.Root
					type="single"
					bind:value={tempValue}
					bind:open={selectOpen}
					onValueChange={handleSelectValueChange}
				>
					<Select.Trigger class="h-6 text-xs px-2 {className} w-full">
						{tempValue ? options.find(opt => opt.value === tempValue)?.label || tempValue : placeholder}
					</Select.Trigger>
					<Select.Content class="z-50">
						<Select.Group>
							{#each options as option (option.value)}
								<Select.Item value={option.value} label={option.label}>
									{option.label}
								</Select.Item>
							{/each}
						</Select.Group>
					</Select.Content>
				</Select.Root>
			</div>
		{:else if type === 'slider'}
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<!-- svelte-ignore a11y_no_static_element_interactions -->
			<div class="w-full px-2 py-1" onclick={(e) => e.stopPropagation()}>
				<RangeSlider
					values={[tempValue]}
					min={min || 1}
					max={max || 100}
					step={1}
					pips
					pipstep={20}
					all="label"
					on:change={handleSliderChange}
					--slider="#6366f1"
					--handle="#6366f1"
					--range="#6366f1"
					--progress="#6366f1"
					--pip="#6366f1"
					--pip-text="#6366f1"
					--pip-active="#6366f1"
					--pip-hover="#6366f1"
				/>
			</div>
		{:else}
			<Input
				bind:this={inputElement}
				bind:value={tempValue}
				{type}
				{placeholder}
				{min}
				{max}
				class="h-6 text-xs px-2 {className}"
				onkeydown={handleKeydown}
				onblur={handleBlur}
				onclick={(e) => e.stopPropagation()}
			/>
		{/if}
	{:else}
		<div
			class="h-6 px-2 text-xs cursor-pointer hover:bg-gray-100 rounded transition-colors flex items-center {className}"
			onclick={startEdit}
			role="button"
			tabindex="0"
			onkeydown={(e) => e.key === 'Enter' && startEdit()}
		>
			{formatDisplayValue(value)}
		</div>
	{/if}
</div>
