<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import * as Select from '$lib/components/ui/select';
	import { Label } from '$lib/components/ui/label';

	let { data } = $props();
	let value = $state('');

	const fruits = [
		{ value: 'apple', label: 'Apple' },
		{ value: 'banana', label: 'Banana' },
		{ value: 'blueberry', label: 'Blueberry' },
		{ value: 'grapes', label: 'Grapes' },
		{ value: 'pineapple', label: 'Pineapple' }
	];

	const triggerContent = $derived(fruits.find((f) => f.value === value)?.label ?? 'Select a fruit');
</script>

<div class="select-node min-w-64">
	<Handle type="target" position={Position.Left} />

	<Card class="border-2 border-green-300 bg-green-50/50">
		<CardHeader class="pb-2">
			<CardTitle class="flex items-center gap-2 text-sm font-semibold text-green-800">
				<span class="text-lg">📋</span>
				{data.label || 'Select Option'}
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-2">
				<Label class="text-xs text-green-700">Choose an option:</Label>
				<Select.Root type="single" name="favoriteFruit" bind:value>
					<Select.Trigger class="w-[180px] border-green-300 focus-visible:border-green-500 focus-visible:ring-green-500/20 bg-green-50/30 data-[state=open]:border-green-500">
						{triggerContent}
					</Select.Trigger>
					<Select.Content class="border-green-300">
						<Select.Group>
							<Select.Label class="text-green-700">Fruits</Select.Label>
							{#each fruits as fruit (fruit.value)}
								<Select.Item
									value={fruit.value}
									label={fruit.label}
									disabled={fruit.value === 'grapes'}
									class="focus:bg-green-50 data-[highlighted]:bg-green-50 data-[state=checked]:bg-green-100 data-[state=checked]:text-green-800"
								>
									{fruit.label}
								</Select.Item>
							{/each}
						</Select.Group>
					</Select.Content>
				</Select.Root>
			</div>
		</CardContent>
	</Card>

	<Handle type="source" position={Position.Right} />
</div>

<style>
	.select-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
</style>
