@config "../tailwind.config.js";
@import "tailwindcss";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.208 0.042 265.755);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.929 0.013 255.508);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* AMQ PLUS color scheme based on #DF6975 */
  --color-amq-primary: #DF6975;
  --color-amq-secondary: #E8899A;
  --color-amq-accent: #75B9DF;
  --color-amq-light: #F5E6E8;
  --color-amq-dark: #B54A5A;
  --color-amq-neutral: #8B7B7A;
  --color-amq-success: #75DF8B;
  --color-amq-warning: #DFB975;

  /* Custom breakpoint for extra small screens */
  --breakpoint-xs: 480px;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-gray-900 text-slate-100; /* Base dark theme */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden; /* Prevent horizontal scroll from large blurred elements */
  }
}

/* Define custom component classes (if any) */
@layer components {
  /* .custom-button {
    @apply px-4 py-2 text-white bg-blue-500 rounded;
  } */
}

/* Define custom utilities */
@layer utilities {
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
  .animation-delay-6000 {
    animation-delay: 6s;
  }
  .animation-delay-8000 {
    animation-delay: 8s;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  /* Custom pulse animation with larger intervals */
  @keyframes slowPulse {
    0%, 100% {
      opacity: 0.05;
      transform: scale(0.95);
    }
    50% {
      opacity: 0.15;
      transform: scale(1.05);
    }
  }

  @keyframes slowPulse2 {
    0%, 100% {
      opacity: 0.08;
      transform: scale(0.98);
    }
    50% {
      opacity: 0.12;
      transform: scale(1.02);
    }
  }

  @keyframes slowPulse3 {
    0%, 100% {
      opacity: 0.06;
      transform: scale(0.96);
    }
    50% {
      opacity: 0.10;
      transform: scale(1.04);
    }
  }

  .animate-slow-pulse {
    animation: slowPulse 8s ease-in-out infinite;
  }

  .animate-slow-pulse-2 {
    animation: slowPulse2 10s ease-in-out infinite;
  }

  .animate-slow-pulse-3 {
    animation: slowPulse3 12s ease-in-out infinite;
  }

  /* Grid beam animations */
  @keyframes gridBeamAppear {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    20% {
      opacity: 1;
      transform: scale(1);
    }
    80% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(0.8);
    }
  }

  .animate-grid-beam {
    animation: gridBeamAppear 4s ease-in-out;
  }

  /* Aurora text effect with continuous linear animation - Light Blues only */
  .aurora-text {
    background: linear-gradient(
      90deg,
      oklch(70% 0.15 220) 0%,        /* light cyan-blue */
      oklch(68.5% 0.169 237.323) 25%, /* sky-500 (your site blue) */
      oklch(75% 0.18 210) 50%,       /* bright sky blue */
      oklch(65% 0.16 250) 75%,       /* vibrant blue */
      oklch(70% 0.15 220) 100%       /* light cyan-blue - seamless loop */
    );
    background-size: 400% 100%;
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    /* 🎛️ SPEED CONTROL: Change this duration to make it slower/faster */
    animation: aurora-flow 20s linear infinite;
  }

  /* Continuous aurora flow animation - slower linear movement */
  @keyframes aurora-flow {
    0% {
      background-position: 0% center;
    }
    100% {
      background-position: 400% center;
    }
  }

  /* Border beam animation for Magic UI BorderBeam component */
  @keyframes border-beam {
    0% {
      offset-distance: 0%;
    }
    100% {
      offset-distance: 100%;
    }
  }

  .animate-border-beam {
    animation: border-beam var(--duration, 15s) linear infinite;
  }
}