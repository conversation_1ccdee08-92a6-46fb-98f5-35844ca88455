/* AMQ PLUS specific styles and color scheme */



@layer components {
  /* AMQ PLUS gradient backgrounds */
  .amq-gradient-bg {
    background: linear-gradient(135deg, 
      rgb(255, 250, 250) 0%, 
      rgb(254, 242, 242) 25%, 
      rgb(253, 230, 238) 50%, 
      rgb(252, 231, 243) 75%, 
      rgb(250, 229, 255) 100%
    );
  }
  
  .amq-gradient-text {
    background: linear-gradient(135deg, 
      rgb(223, 105, 117) 0%, 
      rgb(232, 137, 154) 50%, 
      rgb(117, 185, 223) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* AMQ PLUS button styles - moved to inline classes */

  /* AMQ PLUS card styles - moved to inline classes */

  /* Smooth animations */
  .amq-fade-in {
    animation: amqFadeIn 0.8s ease-out forwards;
  }

  .amq-slide-up {
    animation: amqSlideUp 0.6s ease-out forwards;
  }
  
  /* AMQ PLUS node chart background */
  .amq-nodes-bg {
    background-image:
      radial-gradient(circle at 20% 20%, rgba(223, 105, 117, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(117, 185, 223, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(232, 137, 154, 0.08) 0%, transparent 50%);
  }

  /* Custom animations */
  @keyframes amqFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes amqSlideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive improvements - moved to inline classes */
  @media (max-width: 640px) {
    .amq-gradient-text {
      background-size: 200% 200%;
      animation: amqGradientShift 3s ease infinite;
    }
  }

  @keyframes amqGradientShift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
}
