<!-- SongsAndTypesSelection.svelte - Complex song count, types and selection form -->
<script>
	import { Label } from '$lib/components/ui/label';
	import { Switch } from '$lib/components/ui/switch';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Input } from '$lib/components/ui/input';
	import RangeSlider from 'svelte-range-slider-pips';

	let {
		editedValue = $bindable(),
		config,
		getNodeColor = () => '#6366f1',
		isValid = $bindable(true),
		validationMessage = $bindable(''),
		validationWarning = $bindable('')
	} = $props();

	// Migration function to handle old data structure
	function migrateDataStructure() {
		// Migrate song types if they don't have the new random properties
		Object.keys(editedValue.songTypes).forEach(type => {
			const songType = editedValue.songTypes[type];
			if (songType.random === undefined) {
				songType.random = false;
				songType.percentageMin = Math.max(0, songType.percentage - 10);
				songType.percentageMax = Math.min(100, songType.percentage + 10);
				songType.countMin = Math.max(0, songType.count - 5);
				songType.countMax = Math.min(100, songType.count + 5);
			}
		});

		// Migrate song selection if it doesn't have the new structure
		if (typeof editedValue.songSelection.random === 'number') {
			const oldRandom = editedValue.songSelection.random;
			const oldWatched = editedValue.songSelection.watched;

			editedValue.songSelection.random = {
				value: oldRandom,
				randomRange: false,
				min: Math.max(0, oldRandom - 10),
				max: Math.min(100, oldRandom + 10)
			};

			editedValue.songSelection.watched = {
				value: oldWatched,
				randomRange: false,
				min: Math.max(0, oldWatched - 10),
				max: Math.min(100, oldWatched + 10)
			};
		}
	}

	// Run migration on component initialization
	$effect(() => {
		if (editedValue) {
			migrateDataStructure();
		}
	});

	// Validation function for song count
	function validateSongCount(songCountData) {
		if (typeof songCountData === 'object' && songCountData.random) {
			const min = Number(songCountData.min);
			const max = Number(songCountData.max);
			if (isNaN(min) || isNaN(max) || min < 5 || max > 100 || min > max) {
				isValid = false;
				validationMessage = 'Song count range must be between 5-100 and min ≤ max';
				return false;
			}
		} else {
			const count = typeof songCountData === 'object' ? songCountData.value : songCountData;
			const num = Number(count);
			if (isNaN(num) || num < 5 || num > 100) {
				isValid = false;
				validationMessage = 'Song count must be between 5 and 100';
				return false;
			}
		}
		isValid = true;
		validationMessage = '';
		validationWarning = '';
		return true;
	}

	// Watch for changes in song count to validate
	$effect(() => {
		if (editedValue?.songCount !== undefined) {
			validateSongCount(editedValue.songCount);
		}
	});

	// Get total songs for sliders
	const getTotalSongs = () => {
		if (typeof editedValue?.songCount === 'object') {
			return editedValue.songCount.random ? editedValue.songCount.max : editedValue.songCount.value;
		}
		return editedValue?.songCount || 20;
	};

	// Track if we're currently updating sliders to prevent infinite loops
	let isUpdatingSliders = false;
	let isManualEdit = false;

	// Get enabled song types
	function getEnabledTypes() {
		return Object.keys(editedValue.songTypes).filter(type => editedValue.songTypes[type].enabled);
	}

	// Get current value for song type (handles both static and random)
	function getSongTypeValue(type, mode) {
		const songType = editedValue.songTypes[type];
		if (songType.random) {
			const prop = mode === 'percentage' ? 'percentage' : 'count';
			return songType[prop + 'Min']; // Use min value for calculations
		}
		return songType[mode === 'percentage' ? 'percentage' : 'count'];
	}

	// Get current value for song selection (handles both static and random)
	function getSongSelectionValue(type) {
		const selection = editedValue.songSelection[type];
		if (selection.randomRange) {
			return selection.min; // Use min value for calculations
		}
		return selection.value;
	}

	// Get max value for song selection (for range validation)
	function getSongSelectionMaxValue(type) {
		const selection = editedValue.songSelection[type];
		if (selection.randomRange) {
			return selection.max;
		}
		return selection.value;
	}

	// Check if all enabled song types use random ranges
	function allSongTypesUseRandomRanges() {
		const enabledTypes = getEnabledTypes();
		return enabledTypes.length > 0 && enabledTypes.every(type => editedValue.songTypes[type].random);
	}

	// Check if any song types use random ranges
	function anySongTypesUseRandomRanges() {
		const enabledTypes = getEnabledTypes();
		return enabledTypes.some(type => editedValue.songTypes[type].random);
	}

	// Validate that random ranges can cover 100% when all types are random
	function validateRandomRangesCoverage() {
		if (!allSongTypesUseRandomRanges()) return { valid: true, message: '' };

		const enabledTypes = getEnabledTypes();
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const prop = editedValue.mode === 'percentage' ? 'percentage' : 'count';

		// Check if minimum values sum to <= maxValue and maximum values sum to >= maxValue
		const minSum = enabledTypes.reduce((sum, type) => sum + editedValue.songTypes[type][prop + 'Min'], 0);
		const maxSum = enabledTypes.reduce((sum, type) => sum + editedValue.songTypes[type][prop + 'Max'], 0);

		if (minSum > maxValue) {
			return {
				valid: false,
				message: `Random ranges minimum values sum to ${minSum}${editedValue.mode === 'percentage' ? '%' : ' songs'}, which exceeds the maximum of ${maxValue}${editedValue.mode === 'percentage' ? '%' : ' songs'}.`
			};
		}

		if (maxSum < maxValue) {
			return {
				valid: false,
				message: `Random ranges maximum values sum to ${maxSum}${editedValue.mode === 'percentage' ? '%' : ' songs'}, which is less than the required ${maxValue}${editedValue.mode === 'percentage' ? '%' : ' songs'}.`
			};
		}

		return { valid: true, message: '' };
	}

	// Update linked sliders when one changes - handles mixed random and static ranges
	function updateLinkedSliders(changedType, newValue, isFromSlider = true) {
		if (isUpdatingSliders) return;
		isUpdatingSliders = true;

		const enabledTypes = getEnabledTypes();
		if (enabledTypes.length <= 1) {
			isUpdatingSliders = false;
			return;
		}

		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const prop = editedValue.mode === 'percentage' ? 'percentage' : 'count';
		const changedSongType = editedValue.songTypes[changedType];

		// Separate types into static and random
		const staticTypes = enabledTypes.filter(type => !editedValue.songTypes[type].random);
		const randomTypes = enabledTypes.filter(type => editedValue.songTypes[type].random);
		const otherStaticTypes = staticTypes.filter(t => t !== changedType);
		const otherRandomTypes = randomTypes.filter(t => t !== changedType);

		if (changedSongType.random) {
			// Changed type is random - adjust other sliders
			const usedByRandom = newValue; // Use min value for random ranges
			const usedByOtherRandom = otherRandomTypes.reduce((sum, type) =>
				sum + editedValue.songTypes[type][prop + 'Min'], 0);
			const usedByStatic = staticTypes.reduce((sum, type) =>
				sum + editedValue.songTypes[type][prop], 0);

			const remaining = maxValue - usedByRandom - usedByOtherRandom - usedByStatic;

			// If there are other static types, adjust them
			if (otherStaticTypes.length > 0 && remaining !== 0) {
				distributeValueAmongStaticTypes(otherStaticTypes, remaining, prop);
			}
		} else {
			// Changed type is static - adjust other sliders
			const usedByStatic = staticTypes.reduce((sum, type) =>
				sum + editedValue.songTypes[type][prop], 0);
			const usedByRandomMin = randomTypes.reduce((sum, type) =>
				sum + editedValue.songTypes[type][prop + 'Min'], 0);

			const remaining = maxValue - usedByStatic - usedByRandomMin;

			// If there are other static types, adjust them
			if (otherStaticTypes.length > 0 && remaining !== 0) {
				distributeValueAmongStaticTypes(otherStaticTypes, remaining, prop);
			}
		}

		isUpdatingSliders = false;
	}

	// Helper function to distribute value among static types
	function distributeValueAmongStaticTypes(types, totalValue, prop) {
		if (types.length === 0) return;

		if (types.length === 1) {
			// Only one type, set it to the total value
			editedValue.songTypes[types[0]][prop] = Math.max(0, totalValue);
		} else {
			// Multiple types, distribute proportionally
			const currentTotal = types.reduce((sum, type) =>
				sum + editedValue.songTypes[type][prop], 0);

			if (currentTotal > 0 && totalValue > 0) {
				// Distribute proportionally
				const scaleFactor = totalValue / currentTotal;
				let distributedTotal = 0;

				// Scale all but the last value
				for (let i = 0; i < types.length - 1; i++) {
					const type = types[i];
					const currentValue = editedValue.songTypes[type][prop];
					const newValue = Math.round(currentValue * scaleFactor);
					editedValue.songTypes[type][prop] = Math.max(0, newValue);
					distributedTotal += newValue;
				}

				// Set the last value to ensure exact total
				const lastType = types[types.length - 1];
				editedValue.songTypes[lastType][prop] = Math.max(0, totalValue - distributedTotal);
			} else {
				// If current total is 0 or totalValue is 0, distribute equally
				const equalValue = Math.max(0, Math.floor(totalValue / types.length));
				const remainder = Math.max(0, totalValue % types.length);

				types.forEach((type, index) => {
					const value = equalValue + (index < remainder ? 1 : 0);
					editedValue.songTypes[type][prop] = value;
				});
			}
		}
	}

	// Validate total when manual editing is complete
	function validateSongTypesTotal() {
		if (isUpdatingSliders) return;

		const enabledTypes = getEnabledTypes();
		let songTypesValid = true;
		let songSelectionValid = true;
		let songCountValid = true;
		let errorMessages = [];
		let warningMessages = [];

		// Validate song count
		if (typeof editedValue.songCount === 'object') {
			const count = editedValue.songCount.random ? editedValue.songCount.max : editedValue.songCount.value;
			if (count < 5 || count > 100) {
				songCountValid = false;
				errorMessages.push(`Song count must be between 5 and 100. Current: ${count}`);
			}
		}

		// Validate song types
		if (enabledTypes.length > 0) {
			const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
			const unit = editedValue.mode === 'percentage' ? '%' : ' songs';
			const prop = editedValue.mode === 'percentage' ? 'percentage' : 'count';

			// Check if we have mixed random and static ranges
			const hasRandomRanges = anySongTypesUseRandomRanges();
			const allRandomRanges = allSongTypesUseRandomRanges();

			if (allRandomRanges) {
				// All types use random ranges - validate coverage and maximums

				// Check if any individual random range exceeds maxValue
				let hasExcessiveRange = false;
				enabledTypes.forEach(type => {
					const songType = editedValue.songTypes[type];
					const maxVal = songType[prop + 'Max'];
					if (maxVal > maxValue) {
						hasExcessiveRange = true;
						errorMessages.push(`${type} maximum (${maxVal}${unit}) exceeds the total limit of ${maxValue}${unit}.`);
					}
				});

				if (!hasExcessiveRange) {
					const coverageValidation = validateRandomRangesCoverage();
					if (!coverageValidation.valid) {
						songTypesValid = false;
						errorMessages.push(coverageValidation.message);
					}
				} else {
					songTypesValid = false;
				}
			} else if (hasRandomRanges) {
				// Mixed random and static ranges - more complex validation
				let staticTotal = 0;
				let randomMinTotal = 0;
				let randomMaxTotal = 0;

				enabledTypes.forEach(type => {
					const songType = editedValue.songTypes[type];

					if (songType.random) {
						randomMinTotal += songType[prop + 'Min'];
						randomMaxTotal += songType[prop + 'Max'];
					} else {
						staticTotal += songType[prop];
					}
				});

				// Calculate effective range for random types and show detailed breakdown
				const availableForRandom = maxValue - staticTotal;
				const randomTypes = enabledTypes.filter(type => editedValue.songTypes[type].random);
				const staticTypesList = enabledTypes.filter(type => !editedValue.songTypes[type].random);

				// Build detailed warning message
				let warningParts = [];

				// Show static values
				if (staticTypesList.length > 0) {
					const staticDetails = staticTypesList.map(type => {
						const value = editedValue.songTypes[type][prop];
						return `${type}: ${value}${unit}`;
					}).join(', ');
					warningParts.push(`Static values: ${staticDetails}`);
				}

				// Show random constraints
				if (randomTypes.length === 1) {
					const randomType = randomTypes[0];
					warningParts.push(`${randomType} will be fixed to ${availableForRandom}${unit} (no randomization)`);
				} else if (randomTypes.length > 1) {
					warningParts.push(`Random ranges (${randomTypes.join(', ')}) will be constrained to total ${availableForRandom}${unit} combined`);
				}

				const detailedWarning = warningParts.join('. ');
				warningMessages.push(`Warning: Mixing random ranges with static values limits randomization. ${detailedWarning}. Consider using all static values or all random ranges for better flexibility.`);

				// Check if static + random min > maxValue (impossible)
				if (staticTotal + randomMinTotal > maxValue) {
					songTypesValid = false;
					errorMessages.push(`Static values (${staticTotal}${unit}) plus random minimums (${randomMinTotal}${unit}) exceed ${maxValue}${unit}. This configuration is impossible.`);
				}
				// Check if static + random max < maxValue (impossible)
				else if (staticTotal + randomMaxTotal < maxValue) {
					songTypesValid = false;
					errorMessages.push(`Static values (${staticTotal}${unit}) plus random maximums (${randomMaxTotal}${unit}) are less than ${maxValue}${unit}. This configuration is impossible.`);
				}
			} else {
				// All static ranges - simple validation
				const currentTotal = enabledTypes.reduce((sum, type) =>
					sum + editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'], 0);

				if (currentTotal !== maxValue) {
					songTypesValid = false;
					errorMessages.push(`Song types must total ${maxValue}${unit}. Current total: ${currentTotal}${unit}`);
				}
			}
		}

		// Validate song selection (respects mode)
		const maxSelectionValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const selectionUnit = editedValue.mode === 'percentage' ? '%' : ' songs';

		// Check for mixed random and static ranges in song selection
		const randomIsRange = editedValue.songSelection.random.randomRange;
		const watchedIsRange = editedValue.songSelection.watched.randomRange;
		const hasMixedSelection = randomIsRange !== watchedIsRange;

		if (hasMixedSelection) {
			// Mixed random and static ranges in song selection
			const staticType = randomIsRange ? 'watched' : 'random';
			const randomType = randomIsRange ? 'random' : 'watched';
			const staticValue = editedValue.songSelection[staticType].value;
			const randomMin = editedValue.songSelection[randomType].min;
			const randomMax = editedValue.songSelection[randomType].max;

			// Check if configuration is possible
			if (staticValue + randomMin > maxSelectionValue) {
				songSelectionValid = false;
				errorMessages.push(`Song selection: Static ${staticType} (${staticValue}${selectionUnit}) plus ${randomType} minimum (${randomMin}${selectionUnit}) exceed ${maxSelectionValue}${selectionUnit}.`);
			} else if (staticValue + randomMax < maxSelectionValue) {
				songSelectionValid = false;
				errorMessages.push(`Song selection: Static ${staticType} (${staticValue}${selectionUnit}) plus ${randomType} maximum (${randomMax}${selectionUnit}) are less than ${maxSelectionValue}${selectionUnit}.`);
			} else {
				// Valid but show warning
				const requiredRandomValue = maxSelectionValue - staticValue;
				warningMessages.push(`Warning: Song selection mixing random and static ranges. ${randomType} will be fixed to ${requiredRandomValue}${selectionUnit}, ${staticType} is ${staticValue}${selectionUnit}.`);
			}
		} else if (randomIsRange && watchedIsRange) {
			// Both are random ranges - validate coverage
			const randomMin = editedValue.songSelection.random.min;
			const randomMax = editedValue.songSelection.random.max;
			const watchedMin = editedValue.songSelection.watched.min;
			const watchedMax = editedValue.songSelection.watched.max;

			// Check individual maximums
			if (randomMax > maxSelectionValue) {
				songSelectionValid = false;
				errorMessages.push(`Random selection maximum (${randomMax}${selectionUnit}) exceeds the total limit of ${maxSelectionValue}${selectionUnit}.`);
			}
			if (watchedMax > maxSelectionValue) {
				songSelectionValid = false;
				errorMessages.push(`Watched selection maximum (${watchedMax}${selectionUnit}) exceeds the total limit of ${maxSelectionValue}${selectionUnit}.`);
			}

			// Check coverage if no individual maximums exceed limit
			if (randomMax <= maxSelectionValue && watchedMax <= maxSelectionValue) {
				const minSum = randomMin + watchedMin;
				const maxSum = randomMax + watchedMax;

				if (minSum > maxSelectionValue) {
					songSelectionValid = false;
					errorMessages.push(`Song selection minimum values sum to ${minSum}${selectionUnit}, which exceeds ${maxSelectionValue}${selectionUnit}.`);
				} else if (maxSum < maxSelectionValue) {
					songSelectionValid = false;
					errorMessages.push(`Song selection maximum values sum to ${maxSum}${selectionUnit}, which is less than ${maxSelectionValue}${selectionUnit}.`);
				}
			}
		} else {
			// Both are static - simple validation
			const randomValue = getSongSelectionValue('random');
			const watchedValue = getSongSelectionValue('watched');
			const selectionTotal = randomValue + watchedValue;
			if (selectionTotal !== maxSelectionValue) {
				songSelectionValid = false;
				errorMessages.push(`Song selection must total ${maxSelectionValue}${selectionUnit}. Current total: ${selectionTotal}${selectionUnit}`);
			}
		}

		isValid = songTypesValid && songSelectionValid && songCountValid;
		validationMessage = errorMessages.join(' ');

		// Store warnings separately for display
		if (warningMessages.length > 0) {
			validationWarning = warningMessages.join(' ');
		} else {
			validationWarning = '';
		}
	}

	// Handle slider changes
	function handleSliderChange(type, event) {
		const newValue = event.detail.value;
		editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = newValue;
		updateLinkedSliders(type, newValue, true);
		validateSongTypesTotal();
	}

	// Handle input changes (manual editing - no auto-linking)
	function handleInputChange(type, event) {
		isManualEdit = true;
		const newValue = parseInt(event.target.value) || 0;
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();

		// Clamp value to valid range
		const clampedValue = Math.max(0, Math.min(maxValue, newValue));
		editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = clampedValue;

		// Only validate, don't auto-adjust other sliders
		validateSongTypesTotal();
	}

	// Handle input blur (when user finishes editing)
	function handleInputBlur(type) {
		if (!isManualEdit) return;
		isManualEdit = false;

		// Only validate, don't auto-adjust
		validateSongTypesTotal();
	}

	// Handle song selection slider changes (linked)
	function handleSongSelectionSliderChange(type, event) {
		const newValue = event.detail.value;
		editedValue.songSelection[type].value = newValue;
		updateLinkedSongSelection(type, newValue);
		validateSongTypesTotal();
	}

	// Update linked song selection sliders - handles mixed random and static ranges
	function updateLinkedSongSelection(changedType, newValue) {
		if (isUpdatingSliders) return;
		isUpdatingSliders = true;

		const otherType = changedType === 'random' ? 'watched' : 'random';
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const changedSelection = editedValue.songSelection[changedType];
		const otherSelection = editedValue.songSelection[otherType];

		if (changedSelection.randomRange) {
			// Changed type is random range - use min value for calculation
			const usedByChanged = newValue; // This is the min value
			const remaining = maxValue - usedByChanged;

			if (otherSelection.randomRange) {
				// Both are random ranges - adjust other's min value
				otherSelection.min = Math.max(0, Math.min(maxValue, remaining));
				// Ensure max is at least min + 1 for meaningful range
				if (otherSelection.max <= otherSelection.min) {
					otherSelection.max = Math.min(maxValue, otherSelection.min + 1);
				}
			} else {
				// Other is static - adjust its value
				otherSelection.value = Math.max(0, Math.min(maxValue, remaining));
			}
		} else {
			// Changed type is static - adjust other accordingly
			const remaining = maxValue - newValue;

			if (otherSelection.randomRange) {
				// Other is random range - adjust its min value
				otherSelection.min = Math.max(0, Math.min(maxValue, remaining));
				// Ensure max is at least min + 1 for meaningful range
				if (otherSelection.max <= otherSelection.min) {
					otherSelection.max = Math.min(maxValue, otherSelection.min + 1);
				}
			} else {
				// Both are static - simple adjustment
				otherSelection.value = Math.max(0, Math.min(maxValue, remaining));
			}
		}

		isUpdatingSliders = false;
	}

	// Handle song selection input changes (manual editing - no auto-linking)
	function handleSongSelectionInputChange(type, event) {
		const newValue = parseInt(event.target.value) || 0;
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const clampedValue = Math.max(0, Math.min(maxValue, newValue));
		editedValue.songSelection[type].value = clampedValue;
		validateSongTypesTotal();
	}

	// Quick fix function for song types only
	function quickFixSongTypes() {
		const enabledTypes = getEnabledTypes();

		if (enabledTypes.length > 0) {
			const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
			const prop = editedValue.mode === 'percentage' ? 'percentage' : 'count';

			// Check if all types use random ranges
			if (allSongTypesUseRandomRanges()) {
				// For all random ranges, ensure they can cover 100%
				const equalMin = Math.floor(maxValue / (enabledTypes.length * 2));
				const equalMax = Math.floor(maxValue / enabledTypes.length);

				enabledTypes.forEach((type, index) => {
					const minValue = equalMin;
					const maxValue_type = equalMax + (index === enabledTypes.length - 1 ? maxValue % enabledTypes.length : 0);

					editedValue.songTypes[type][prop + 'Min'] = minValue;
					editedValue.songTypes[type][prop + 'Max'] = Math.max(maxValue_type, minValue + 1);
				});
			} else if (anySongTypesUseRandomRanges()) {
				// Mixed random and static - adjust static values to work with random ranges
				let staticTotal = 0;
				let randomMinTotal = 0;
				let staticTypes = [];

				enabledTypes.forEach(type => {
					const songType = editedValue.songTypes[type];
					if (songType.random) {
						randomMinTotal += songType[prop + 'Min'];
					} else {
						staticTotal += songType[prop];
						staticTypes.push(type);
					}
				});

				// Adjust static values to fit with random minimums
				const availableForStatic = maxValue - randomMinTotal;
				if (staticTypes.length > 0 && availableForStatic > 0) {
					const equalStaticValue = Math.floor(availableForStatic / staticTypes.length);
					const remainder = availableForStatic % staticTypes.length;

					staticTypes.forEach((type, index) => {
						const value = equalStaticValue + (index < remainder ? 1 : 0);
						editedValue.songTypes[type][prop] = value;
					});
				}
			} else {
				// All static ranges - original logic
				const currentTotal = enabledTypes.reduce((sum, type) =>
					sum + editedValue.songTypes[type][prop], 0);

				if (currentTotal !== maxValue) {
					// Proportionally adjust values to sum to maxValue
					if (currentTotal > 0) {
						const scaleFactor = maxValue / currentTotal;
						let adjustedTotal = 0;

						// Apply scaling and track total
						enabledTypes.forEach((type, index) => {
							const currentValue = editedValue.songTypes[type][prop];
							const scaledValue = Math.round(currentValue * scaleFactor);
							editedValue.songTypes[type][prop] = scaledValue;
							adjustedTotal += scaledValue;
						});

						// Handle rounding errors by adjusting the largest value
						const difference = maxValue - adjustedTotal;
						if (difference !== 0) {
							// Find the type with the largest value to adjust
							const largestType = enabledTypes.reduce((max, type) => {
								const currentValue = editedValue.songTypes[type][prop];
								const maxValue_comp = editedValue.songTypes[max][prop];
								return currentValue > maxValue_comp ? type : max;
							});

							editedValue.songTypes[largestType][prop] += difference;
						}
					} else {
						// If all values are 0, distribute equally
						const equalShare = Math.floor(maxValue / enabledTypes.length);
						const remainder = maxValue % enabledTypes.length;

						enabledTypes.forEach((type, index) => {
							const value = equalShare + (index < remainder ? 1 : 0);
							editedValue.songTypes[type][prop] = value;
						});
					}
				}
			}
		}

		validateSongTypesTotal();
	}

	// Quick fix function for song selection only
	function quickFixSongSelection() {
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const randomValue = getSongSelectionValue('random');
		const watchedValue = getSongSelectionValue('watched');
		const selectionTotal = randomValue + watchedValue;

		if (selectionTotal !== maxValue) {
			if (selectionTotal > 0) {
				// Proportionally adjust to sum to maxValue
				const scaleFactor = maxValue / selectionTotal;
				const adjustedRandom = Math.round(randomValue * scaleFactor);
				const adjustedWatched = maxValue - adjustedRandom; // Ensure exact total

				// Update the appropriate values based on whether they're using ranges or not
				if (editedValue.songSelection.random.randomRange) {
					editedValue.songSelection.random.min = adjustedRandom;
					editedValue.songSelection.random.max = Math.min(maxValue, adjustedRandom + 10); // Add some range
				} else {
					editedValue.songSelection.random.value = adjustedRandom;
				}

				if (editedValue.songSelection.watched.randomRange) {
					editedValue.songSelection.watched.min = adjustedWatched;
					editedValue.songSelection.watched.max = Math.min(maxValue, adjustedWatched + 10); // Add some range
				} else {
					editedValue.songSelection.watched.value = adjustedWatched;
				}
			} else {
				// If both are 0, set to equal split
				const halfValue = Math.floor(maxValue / 2);

				if (editedValue.songSelection.random.randomRange) {
					editedValue.songSelection.random.min = halfValue;
					editedValue.songSelection.random.max = Math.min(maxValue, halfValue + 10);
				} else {
					editedValue.songSelection.random.value = halfValue;
				}

				if (editedValue.songSelection.watched.randomRange) {
					editedValue.songSelection.watched.min = maxValue - halfValue;
					editedValue.songSelection.watched.max = maxValue;
				} else {
					editedValue.songSelection.watched.value = maxValue - halfValue;
				}
			}
		}

		validateSongTypesTotal();
	}

	// Quick fix function to snap all values to nearest correct settings
	function quickFixValues() {
		quickFixSongTypes();
		quickFixSongSelection();
	}

	// Convert values when mode changes
	let previousMode = editedValue.mode;
	$effect(() => {
		if (previousMode !== editedValue.mode) {
			convertValuesForModeChange(previousMode, editedValue.mode);
			previousMode = editedValue.mode;
		}
	});

	// Convert values between percentage and count modes
	function convertValuesForModeChange(fromMode, toMode) {
		const totalSongs = getTotalSongs();

		// Convert song types
		const enabledTypes = getEnabledTypes();
		enabledTypes.forEach(type => {
			const songType = editedValue.songTypes[type];
			if (fromMode === 'percentage' && toMode === 'count') {
				// Convert percentage to count
				songType.count = Math.round((songType.percentage / 100) * totalSongs);
				songType.countMin = Math.round((songType.percentageMin / 100) * totalSongs);
				songType.countMax = Math.round((songType.percentageMax / 100) * totalSongs);
			} else if (fromMode === 'count' && toMode === 'percentage') {
				// Convert count to percentage
				songType.percentage = Math.round((songType.count / totalSongs) * 100);
				songType.percentageMin = Math.round((songType.countMin / totalSongs) * 100);
				songType.percentageMax = Math.round((songType.countMax / totalSongs) * 100);
			}
		});

		// Convert song selection
		if (fromMode === 'percentage' && toMode === 'count') {
			// Convert percentage to count
			editedValue.songSelection.random.value = Math.round((editedValue.songSelection.random.value / 100) * totalSongs);
			editedValue.songSelection.random.min = Math.round((editedValue.songSelection.random.min / 100) * totalSongs);
			editedValue.songSelection.random.max = Math.round((editedValue.songSelection.random.max / 100) * totalSongs);
			editedValue.songSelection.watched.value = Math.round((editedValue.songSelection.watched.value / 100) * totalSongs);
			editedValue.songSelection.watched.min = Math.round((editedValue.songSelection.watched.min / 100) * totalSongs);
			editedValue.songSelection.watched.max = Math.round((editedValue.songSelection.watched.max / 100) * totalSongs);
		} else if (fromMode === 'count' && toMode === 'percentage') {
			// Convert count to percentage
			editedValue.songSelection.random.value = Math.round((editedValue.songSelection.random.value / totalSongs) * 100);
			editedValue.songSelection.random.min = Math.round((editedValue.songSelection.random.min / totalSongs) * 100);
			editedValue.songSelection.random.max = Math.round((editedValue.songSelection.random.max / totalSongs) * 100);
			editedValue.songSelection.watched.value = Math.round((editedValue.songSelection.watched.value / totalSongs) * 100);
			editedValue.songSelection.watched.min = Math.round((editedValue.songSelection.watched.min / totalSongs) * 100);
			editedValue.songSelection.watched.max = Math.round((editedValue.songSelection.watched.max / totalSongs) * 100);
		}

		// Ensure totals are correct after conversion
		setTimeout(() => {
			const enabledTypes = getEnabledTypes();
			if (enabledTypes.length > 0) {
				const maxValue = toMode === 'percentage' ? 100 : totalSongs;

				// Fix song types total
				const currentSongTypesTotal = enabledTypes.reduce((sum, type) =>
					sum + editedValue.songTypes[type][toMode === 'percentage' ? 'percentage' : 'count'], 0);

				if (currentSongTypesTotal !== maxValue && currentSongTypesTotal > 0) {
					const scaleFactor = maxValue / currentSongTypesTotal;
					let distributedTotal = 0;

					// Scale all but the last value
					for (let i = 0; i < enabledTypes.length - 1; i++) {
						const type = enabledTypes[i];
						const currentValue = editedValue.songTypes[type][toMode === 'percentage' ? 'percentage' : 'count'];
						const newValue = Math.round(currentValue * scaleFactor);
						editedValue.songTypes[type][toMode === 'percentage' ? 'percentage' : 'count'] = newValue;
						distributedTotal += newValue;
					}

					// Set the last value to ensure exact total
					const lastType = enabledTypes[enabledTypes.length - 1];
					editedValue.songTypes[lastType][toMode === 'percentage' ? 'percentage' : 'count'] = maxValue - distributedTotal;
				}

				// Fix song selection total
				const randomValue = getSongSelectionValue('random');
				const watchedValue = getSongSelectionValue('watched');
				const currentSelectionTotal = randomValue + watchedValue;
				if (currentSelectionTotal !== maxValue && currentSelectionTotal > 0) {
					const scaleFactor = maxValue / currentSelectionTotal;
					const adjustedRandom = Math.round(randomValue * scaleFactor);
					const adjustedWatched = maxValue - adjustedRandom;

					// Update the appropriate values based on whether they're using ranges or not
					if (editedValue.songSelection.random.randomRange) {
						editedValue.songSelection.random.min = adjustedRandom;
					} else {
						editedValue.songSelection.random.value = adjustedRandom;
					}

					if (editedValue.songSelection.watched.randomRange) {
						editedValue.songSelection.watched.min = adjustedWatched;
					} else {
						editedValue.songSelection.watched.value = adjustedWatched;
					}
				}
			}

			validateSongTypesTotal();
		}, 0);
	}

	// Watch for checkbox changes to redistribute values
	$effect(() => {
		const enabledTypes = getEnabledTypes();
		if (enabledTypes.length === 0) return;

		// If only one type is enabled, give it the full value
		if (enabledTypes.length === 1) {
			const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
			editedValue.songTypes[enabledTypes[0]][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = maxValue;
		}

		validateSongTypesTotal();
	});

	// Watch for random song count changes and auto-switch to percentage mode
	$effect(() => {
		if (editedValue.songCount && typeof editedValue.songCount === 'object' && editedValue.songCount.random) {
			// Force percentage mode when random song count is enabled
			if (editedValue.mode !== 'percentage') {
				editedValue.mode = 'percentage';
			}
		}
	});
</script>

<div class="space-y-4">

	<!-- Song Count Section -->
	<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
		<div class="flex items-center justify-between mb-4">
			<Label class="text-lg font-semibold text-gray-800">Number of Songs</Label>
			<!-- Mode Toggle -->
			<div class="flex items-center space-x-3 bg-gray-50 px-3 py-2 rounded-lg border">
				<Label class="text-sm font-medium text-gray-700">Mode:</Label>
				<Label for="mode-switch" class="text-sm {editedValue.mode === 'percentage' ? 'font-semibold text-blue-600' : 'text-gray-500'}">%</Label>
				<Switch
					id="mode-switch"
					checked={editedValue.mode === 'count'}
					disabled={editedValue.songCount && typeof editedValue.songCount === 'object' && editedValue.songCount.random}
					onCheckedChange={(checked) => editedValue.mode = checked ? 'count' : 'percentage'}
				/>
				<Label for="mode-switch" class="text-sm {editedValue.mode === 'count' ? 'font-semibold text-blue-600' : editedValue.songCount && typeof editedValue.songCount === 'object' && editedValue.songCount.random ? 'text-gray-400' : 'text-gray-500'}">Count</Label>
			</div>
		</div>
		<div class="space-y-6">
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-2">
					<Checkbox bind:checked={editedValue.songCount.random} id="song-count-random" />
					<Label for="song-count-random" class="text-sm font-normal">Use random range</Label>
				</div>
				<div class="text-xs text-gray-500">
					{#if editedValue.songCount && typeof editedValue.songCount === 'object' && editedValue.songCount.random}
						Songs: {editedValue.songCount.min}-{editedValue.songCount.max}
					{:else}
						Songs: {getTotalSongs()}
					{/if}
				</div>
			</div>
			{#if editedValue.songCount && typeof editedValue.songCount === 'object' && editedValue.songCount.random}
				<div class="text-xs text-orange-600 bg-orange-50 p-2 rounded border border-orange-200">
					% mode required for random count
				</div>
			{/if}

			<div class="px-4 py-6 bg-gray-50 border border-gray-200 rounded-lg">
				{#if editedValue.songCount.random}
					<!-- Range Slider (2 heads) -->
					<div class="space-y-4">
						<RangeSlider
							values={[editedValue.songCount.min, editedValue.songCount.max]}
							min={5} max={100} step={1} range pushy pips pipstep={10} all="label"
							on:change={(e) => {
								editedValue.songCount.min = e.detail.values[0];
								editedValue.songCount.max = e.detail.values[1];
							}}
							--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
						/>
						<div class="grid grid-cols-3 gap-4 text-center">
							<div class="p-3 bg-white border rounded-lg">
								<div class="text-xs text-gray-600">Min Songs</div>
								<div class="text-lg font-bold" style="color: {getNodeColor()}">{editedValue.songCount.min}</div>
							</div>
							<div class="p-3 bg-white border rounded-lg">
								<div class="text-xs text-gray-600">Max Songs</div>
								<div class="text-lg font-bold" style="color: {getNodeColor()}">{editedValue.songCount.max}</div>
							</div>
							<div class="p-3 bg-white border rounded-lg">
								<div class="text-xs text-gray-600">Range</div>
								<div class="text-lg font-bold" style="color: {getNodeColor()}">{editedValue.songCount.max - editedValue.songCount.min}</div>
							</div>
						</div>
					</div>
				{:else}
					<!-- Single Slider (1 head) -->
					<div class="space-y-4">
						<RangeSlider
							values={[editedValue.songCount.value]}
							min={5} max={100} step={1} pips pipstep={10} all="label"
							on:change={(e) => editedValue.songCount.value = e.detail.value}
							--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
						/>
						<div class="text-center">
							<div class="p-3 bg-white border rounded-lg inline-block">
								<div class="text-xs text-gray-600">Number of Songs</div>
								<div class="text-lg font-bold" style="color: {getNodeColor()}">{editedValue.songCount.value}</div>
							</div>
						</div>
					</div>
				{/if}
			</div>

			{#if !isValid}
				<div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
					<p class="text-sm text-red-600 mb-2">{validationMessage}</p>
					<div class="flex gap-2">
						{#if validationMessage.includes('Song types must total') || validationMessage.includes('Random ranges') || validationMessage.includes('Static values') || validationMessage.includes('maximum') || validationMessage.includes('minimum')}
							<button
								type="button"
								onclick={quickFixSongTypes}
								class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
							>
								Fix Song Types
							</button>
						{/if}
						{#if validationMessage.includes('Song selection must total') || validationMessage.includes('Song selection:')}
							<button
								type="button"
								onclick={quickFixSongSelection}
								class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
							>
								Fix Song Selection
							</button>
						{/if}
						<button
							type="button"
							onclick={quickFixValues}
							class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
						>
							Fix All
						</button>
					</div>
				</div>
			{/if}

			{#if validationWarning}
				<div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
					<p class="text-sm text-yellow-700">{validationWarning}</p>
				</div>
			{/if}
		</div>
	</div>

	<div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
		<!-- Song Types Section -->
		<div class="p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
			<Label class="block mb-3 text-base font-semibold text-gray-800">Song Types</Label>
			<div class="space-y-4">
				<!-- Openings -->
				<div class="space-y-2">
					<div class="flex items-center justify-between">
						<div class="flex items-center space-x-2">
							<Checkbox bind:checked={editedValue.songTypes.openings.enabled} id="openings" />
							<Label for="openings" class="text-sm">Openings</Label>
						</div>
						{#if editedValue.songTypes.openings.enabled}
							<div class="flex items-center space-x-2">
								<Checkbox bind:checked={editedValue.songTypes.openings.random} id="openings-random" />
								<Label for="openings-random" class="text-xs text-gray-600">Random range</Label>
							</div>
						{/if}
					</div>
					{#if editedValue.songTypes.openings.enabled}
						{#if editedValue.songTypes.openings.random}
							<!-- Random Range Mode -->
							{#if editedValue.mode === 'percentage'}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.openings.percentageMin, editedValue.songTypes.openings.percentageMax]}
										min={0} max={100} step={1} range pushy pips pipstep={25} all="label"
										on:change={(e) => {
											editedValue.songTypes.openings.percentageMin = e.detail.values[0];
											editedValue.songTypes.openings.percentageMax = e.detail.values[1];
											validateSongTypesTotal();
										}}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center space-x-4">
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Min:</span>
											<Input
												type="number"
												value={editedValue.songTypes.openings.percentageMin}
												min={0} max={100}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.openings.percentageMin = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Max:</span>
											<Input
												type="number"
												value={editedValue.songTypes.openings.percentageMax}
												min={0} max={100}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.openings.percentageMax = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
									</div>
								</div>
							{:else}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.openings.countMin, editedValue.songTypes.openings.countMax]}
										min={0} max={getTotalSongs()} step={1} range pushy pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
										on:change={(e) => {
											editedValue.songTypes.openings.countMin = e.detail.values[0];
											editedValue.songTypes.openings.countMax = e.detail.values[1];
											validateSongTypesTotal();
										}}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center space-x-4">
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Min:</span>
											<Input
												type="number"
												value={editedValue.songTypes.openings.countMin}
												min={0} max={getTotalSongs()}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.openings.countMin = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Max:</span>
											<Input
												type="number"
												value={editedValue.songTypes.openings.countMax}
												min={0} max={getTotalSongs()}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.openings.countMax = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
									</div>
								</div>
							{/if}
						{:else}
							<!-- Static Value Mode -->
							{#if editedValue.mode === 'percentage'}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.openings.percentage]}
										min={0} max={100} step={1} pips pipstep={25} all="label"
										on:change={(e) => handleSliderChange('openings', e)}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center">
										<div class="flex items-center space-x-1">
											<Input
												type="number"
												value={editedValue.songTypes.openings.percentage}
												min={0} max={100}
												class="w-16 h-6 text-xs text-center px-1"
												oninput={(e) => handleInputChange('openings', e)}
												onblur={() => handleInputBlur('openings')}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
									</div>
								</div>
							{:else}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.openings.count]}
										min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
										on:change={(e) => handleSliderChange('openings', e)}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center">
										<div class="flex items-center space-x-1">
											<Input
												type="number"
												value={editedValue.songTypes.openings.count}
												min={0} max={getTotalSongs()}
												class="w-16 h-6 text-xs text-center px-1"
												oninput={(e) => handleInputChange('openings', e)}
												onblur={() => handleInputBlur('openings')}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
									</div>
								</div>
							{/if}
						{/if}
					{/if}
				</div>

				<!-- Endings -->
				<div class="space-y-2">
					<div class="flex items-center justify-between">
						<div class="flex items-center space-x-2">
							<Checkbox bind:checked={editedValue.songTypes.endings.enabled} id="endings" />
							<Label for="endings" class="text-sm">Endings</Label>
						</div>
						{#if editedValue.songTypes.endings.enabled}
							<div class="flex items-center space-x-2">
								<Checkbox bind:checked={editedValue.songTypes.endings.random} id="endings-random" />
								<Label for="endings-random" class="text-xs text-gray-600">Random range</Label>
							</div>
						{/if}
					</div>
					{#if editedValue.songTypes.endings.enabled}
						{#if editedValue.songTypes.endings.random}
							<!-- Random Range Mode -->
							{#if editedValue.mode === 'percentage'}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.endings.percentageMin, editedValue.songTypes.endings.percentageMax]}
										min={0} max={100} step={1} range pushy pips pipstep={25} all="label"
										on:change={(e) => {
											editedValue.songTypes.endings.percentageMin = e.detail.values[0];
											editedValue.songTypes.endings.percentageMax = e.detail.values[1];
											validateSongTypesTotal();
										}}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center space-x-4">
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Min:</span>
											<Input
												type="number"
												value={editedValue.songTypes.endings.percentageMin}
												min={0} max={100}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.endings.percentageMin = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Max:</span>
											<Input
												type="number"
												value={editedValue.songTypes.endings.percentageMax}
												min={0} max={100}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.endings.percentageMax = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
									</div>
								</div>
							{:else}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.endings.countMin, editedValue.songTypes.endings.countMax]}
										min={0} max={getTotalSongs()} step={1} range pushy pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
										on:change={(e) => {
											editedValue.songTypes.endings.countMin = e.detail.values[0];
											editedValue.songTypes.endings.countMax = e.detail.values[1];
											validateSongTypesTotal();
										}}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center space-x-4">
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Min:</span>
											<Input
												type="number"
												value={editedValue.songTypes.endings.countMin}
												min={0} max={getTotalSongs()}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.endings.countMin = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Max:</span>
											<Input
												type="number"
												value={editedValue.songTypes.endings.countMax}
												min={0} max={getTotalSongs()}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.endings.countMax = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
									</div>
								</div>
							{/if}
						{:else}
							<!-- Static Value Mode -->
							{#if editedValue.mode === 'percentage'}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.endings.percentage]}
										min={0} max={100} step={1} pips pipstep={25} all="label"
										on:change={(e) => handleSliderChange('endings', e)}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center">
										<div class="flex items-center space-x-1">
											<Input
												type="number"
												value={editedValue.songTypes.endings.percentage}
												min={0} max={100}
												class="w-16 h-6 text-xs text-center px-1"
												oninput={(e) => handleInputChange('endings', e)}
												onblur={() => handleInputBlur('endings')}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
									</div>
								</div>
							{:else}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.endings.count]}
										min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
										on:change={(e) => handleSliderChange('endings', e)}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center">
										<div class="flex items-center space-x-1">
											<Input
												type="number"
												value={editedValue.songTypes.endings.count}
												min={0} max={getTotalSongs()}
												class="w-16 h-6 text-xs text-center px-1"
												oninput={(e) => handleInputChange('endings', e)}
												onblur={() => handleInputBlur('endings')}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
									</div>
								</div>
							{/if}
						{/if}
					{/if}
				</div>

				<!-- Inserts -->
				<div class="space-y-2">
					<div class="flex items-center justify-between">
						<div class="flex items-center space-x-2">
							<Checkbox bind:checked={editedValue.songTypes.inserts.enabled} id="inserts" />
							<Label for="inserts" class="text-sm">Inserts</Label>
						</div>
						{#if editedValue.songTypes.inserts.enabled}
							<div class="flex items-center space-x-2">
								<Checkbox bind:checked={editedValue.songTypes.inserts.random} id="inserts-random" />
								<Label for="inserts-random" class="text-xs text-gray-600">Random range</Label>
							</div>
						{/if}
					</div>
					{#if editedValue.songTypes.inserts.enabled}
						{#if editedValue.songTypes.inserts.random}
							<!-- Random Range Mode -->
							{#if editedValue.mode === 'percentage'}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.inserts.percentageMin, editedValue.songTypes.inserts.percentageMax]}
										min={0} max={100} step={1} range pushy pips pipstep={25} all="label"
										on:change={(e) => {
											editedValue.songTypes.inserts.percentageMin = e.detail.values[0];
											editedValue.songTypes.inserts.percentageMax = e.detail.values[1];
											validateSongTypesTotal();
										}}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center space-x-4">
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Min:</span>
											<Input
												type="number"
												value={editedValue.songTypes.inserts.percentageMin}
												min={0} max={100}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.inserts.percentageMin = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Max:</span>
											<Input
												type="number"
												value={editedValue.songTypes.inserts.percentageMax}
												min={0} max={100}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.inserts.percentageMax = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
									</div>
								</div>
							{:else}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.inserts.countMin, editedValue.songTypes.inserts.countMax]}
										min={0} max={getTotalSongs()} step={1} range pushy pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
										on:change={(e) => {
											editedValue.songTypes.inserts.countMin = e.detail.values[0];
											editedValue.songTypes.inserts.countMax = e.detail.values[1];
											validateSongTypesTotal();
										}}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center space-x-4">
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Min:</span>
											<Input
												type="number"
												value={editedValue.songTypes.inserts.countMin}
												min={0} max={getTotalSongs()}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.inserts.countMin = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
										<div class="flex items-center space-x-1">
											<span class="text-xs text-gray-600">Max:</span>
											<Input
												type="number"
												value={editedValue.songTypes.inserts.countMax}
												min={0} max={getTotalSongs()}
												class="w-12 h-6 text-xs text-center px-1"
												oninput={(e) => {
													editedValue.songTypes.inserts.countMax = parseInt(e.target.value) || 0;
													validateSongTypesTotal();
												}}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
									</div>
								</div>
							{/if}
						{:else}
							<!-- Static Value Mode -->
							{#if editedValue.mode === 'percentage'}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.inserts.percentage]}
										min={0} max={100} step={1} pips pipstep={25} all="label"
										on:change={(e) => handleSliderChange('inserts', e)}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center">
										<div class="flex items-center space-x-1">
											<Input
												type="number"
												value={editedValue.songTypes.inserts.percentage}
												min={0} max={100}
												class="w-16 h-6 text-xs text-center px-1"
												oninput={(e) => handleInputChange('inserts', e)}
												onblur={() => handleInputBlur('inserts')}
											/>
											<span class="text-xs text-gray-600">%</span>
										</div>
									</div>
								</div>
							{:else}
								<div class="px-2">
									<RangeSlider
										values={[editedValue.songTypes.inserts.count]}
										min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
										on:change={(e) => handleSliderChange('inserts', e)}
										--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
									/>
									<div class="mt-1 flex justify-center">
										<div class="flex items-center space-x-1">
											<Input
												type="number"
												value={editedValue.songTypes.inserts.count}
												min={0} max={getTotalSongs()}
												class="w-16 h-6 text-xs text-center px-1"
												oninput={(e) => handleInputChange('inserts', e)}
												onblur={() => handleInputBlur('inserts')}
											/>
											<span class="text-xs text-gray-600">songs</span>
										</div>
									</div>
								</div>
							{/if}
						{/if}
					{/if}
				</div>
			</div>
		</div>

		<!-- Song Selection Section -->
		<div class="p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
			<Label class="block mb-3 text-base font-semibold text-gray-800">Song Selection</Label>
			<div class="space-y-4">
				<!-- Random -->
				<div class="space-y-2">
					<div class="flex items-center justify-between">
						<Label class="text-sm">Random</Label>
						<div class="flex items-center space-x-2">
							<Checkbox bind:checked={editedValue.songSelection.random.randomRange} id="random-range" />
							<Label for="random-range" class="text-xs text-gray-600">Random range</Label>
						</div>
					</div>
					<div class="px-2">
						{#if editedValue.songSelection.random.randomRange}
							<!-- Random Range Mode -->
							{#if editedValue.mode === 'percentage'}
								<RangeSlider
									values={[editedValue.songSelection.random.min, editedValue.songSelection.random.max]}
									min={0} max={100} step={1} range pushy pips pipstep={25} all="label"
									on:change={(e) => {
										editedValue.songSelection.random.min = e.detail.values[0];
										editedValue.songSelection.random.max = e.detail.values[1];
										updateLinkedSongSelection('random', editedValue.songSelection.random.min);
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<span class="text-xs text-gray-600">Min:</span>
										<Input
											type="number"
											value={editedValue.songSelection.random.min}
											min={0} max={100}
											class="w-12 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.songSelection.random.min = parseInt(e.target.value) || 0;
												updateLinkedSongSelection('random', editedValue.songSelection.random.min);
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
									<div class="flex items-center space-x-1">
										<span class="text-xs text-gray-600">Max:</span>
										<Input
											type="number"
											value={editedValue.songSelection.random.max}
											min={0} max={100}
											class="w-12 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.songSelection.random.max = parseInt(e.target.value) || 0;
												validateSongTypesTotal();
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							{:else}
								<RangeSlider
									values={[editedValue.songSelection.random.min, editedValue.songSelection.random.max]}
									min={0} max={getTotalSongs()} step={1} range pushy pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => {
										editedValue.songSelection.random.min = e.detail.values[0];
										editedValue.songSelection.random.max = e.detail.values[1];
										updateLinkedSongSelection('random', editedValue.songSelection.random.min);
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<span class="text-xs text-gray-600">Min:</span>
										<Input
											type="number"
											value={editedValue.songSelection.random.min}
											min={0} max={getTotalSongs()}
											class="w-12 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.songSelection.random.min = parseInt(e.target.value) || 0;
												updateLinkedSongSelection('random', editedValue.songSelection.random.min);
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
									<div class="flex items-center space-x-1">
										<span class="text-xs text-gray-600">Max:</span>
										<Input
											type="number"
											value={editedValue.songSelection.random.max}
											min={0} max={getTotalSongs()}
											class="w-12 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.songSelection.random.max = parseInt(e.target.value) || 0;
												validateSongTypesTotal();
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							{/if}
						{:else}
							<!-- Static Value Mode -->
							{#if editedValue.mode === 'percentage'}
								<RangeSlider
									values={[editedValue.songSelection.random.value]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => handleSongSelectionSliderChange('random', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songSelection.random.value}
											min={0} max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleSongSelectionInputChange('random', e)}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							{:else}
								<RangeSlider
									values={[editedValue.songSelection.random.value]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => handleSongSelectionSliderChange('random', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songSelection.random.value}
											min={0} max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleSongSelectionInputChange('random', e)}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							{/if}
						{/if}
					</div>
				</div>

				<!-- Watched -->
				<div class="space-y-2">
					<div class="flex items-center justify-between">
						<Label class="text-sm">Watched</Label>
						<div class="flex items-center space-x-2">
							<Checkbox bind:checked={editedValue.songSelection.watched.randomRange} id="watched-range" />
							<Label for="watched-range" class="text-xs text-gray-600">Random range</Label>
						</div>
					</div>
					<div class="px-2">
						{#if editedValue.songSelection.watched.randomRange}
							<!-- Random Range Mode -->
							{#if editedValue.mode === 'percentage'}
								<RangeSlider
									values={[editedValue.songSelection.watched.min, editedValue.songSelection.watched.max]}
									min={0} max={100} step={1} range pushy pips pipstep={25} all="label"
									on:change={(e) => {
										editedValue.songSelection.watched.min = e.detail.values[0];
										editedValue.songSelection.watched.max = e.detail.values[1];
										updateLinkedSongSelection('watched', editedValue.songSelection.watched.min);
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<span class="text-xs text-gray-600">Min:</span>
										<Input
											type="number"
											value={editedValue.songSelection.watched.min}
											min={0} max={100}
											class="w-12 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.songSelection.watched.min = parseInt(e.target.value) || 0;
												updateLinkedSongSelection('watched', editedValue.songSelection.watched.min);
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
									<div class="flex items-center space-x-1">
										<span class="text-xs text-gray-600">Max:</span>
										<Input
											type="number"
											value={editedValue.songSelection.watched.max}
											min={0} max={100}
											class="w-12 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.songSelection.watched.max = parseInt(e.target.value) || 0;
												validateSongTypesTotal();
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							{:else}
								<RangeSlider
									values={[editedValue.songSelection.watched.min, editedValue.songSelection.watched.max]}
									min={0} max={getTotalSongs()} step={1} range pushy pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => {
										editedValue.songSelection.watched.min = e.detail.values[0];
										editedValue.songSelection.watched.max = e.detail.values[1];
										updateLinkedSongSelection('watched', editedValue.songSelection.watched.min);
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<span class="text-xs text-gray-600">Min:</span>
										<Input
											type="number"
											value={editedValue.songSelection.watched.min}
											min={0} max={getTotalSongs()}
											class="w-12 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.songSelection.watched.min = parseInt(e.target.value) || 0;
												updateLinkedSongSelection('watched', editedValue.songSelection.watched.min);
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
									<div class="flex items-center space-x-1">
										<span class="text-xs text-gray-600">Max:</span>
										<Input
											type="number"
											value={editedValue.songSelection.watched.max}
											min={0} max={getTotalSongs()}
											class="w-12 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.songSelection.watched.max = parseInt(e.target.value) || 0;
												validateSongTypesTotal();
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							{/if}
						{:else}
							<!-- Static Value Mode -->
							{#if editedValue.mode === 'percentage'}
								<RangeSlider
									values={[editedValue.songSelection.watched.value]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => handleSongSelectionSliderChange('watched', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songSelection.watched.value}
											min={0} max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleSongSelectionInputChange('watched', e)}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							{:else}
								<RangeSlider
									values={[editedValue.songSelection.watched.value]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => handleSongSelectionSliderChange('watched', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songSelection.watched.value}
											min={0} max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleSongSelectionInputChange('watched', e)}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							{/if}
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
