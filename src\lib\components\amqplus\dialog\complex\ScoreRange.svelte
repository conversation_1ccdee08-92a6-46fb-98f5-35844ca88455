<!-- ScoreRange.svelte - Complex score range form -->
<script>
	import { Label } from '$lib/components/ui/label';

	let {
		editedValue = $bindable(),
		config,
		getNodeColor = () => '#6366f1'
	} = $props();
</script>

<div class="space-y-8">
	<div class="text-center">
		<Label class="text-2xl font-bold text-gray-800">{config.label}</Label>
		<p class="mt-2 text-gray-600">Configure score range preferences</p>
	</div>

	<div class="p-8 text-center text-gray-500">
		<p>Score Range form will be implemented here.</p>
		<p class="text-sm mt-2">This is a placeholder for the complex score range configuration.</p>
	</div>
</div>
