<script>
	import { <PERSON><PERSON>, <PERSON>sition } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import NodeEditDialog from './NodeEditDialog.svelte';
	import InlineEdit from './InlineEdit.svelte';

	let { data } = $props();

	// Extract handlers from data
	const onValueChange = data.onValueChange || (() => {});
	const onModalClose = data.onModalClose || (() => {});

	// Dialog state
	let dialogOpen = $state(false);
	let nodeDataForDialog = $state(null);

	// Inline editing state
	let isInlineEditing = $state(false);

	// Local state for current value to avoid mutating props
	let currentValue = $state(data.currentValue);

	// Sync local state with prop changes
	$effect(() => {
		currentValue = data.currentValue;
	});



	// Simple field configurations for inline editing
	const simpleFieldConfigs = {
		// Mode Zone Settings
		'scoring': {
			type: 'select',
			options: [
				{ value: 'count', label: 'Count' },
				{ value: 'hint', label: 'Hint' },
				{ value: 'speed', label: 'Speed' }
			]
		},
		'answering': {
			type: 'select',
			options: [
				{ value: 'typing', label: 'Typing' },
				{ value: 'mix', label: 'Mix' },
				{ value: 'multiple-choice', label: 'Multiple Choice' }
			]
		},

		// General Zone Settings
		'players': {
			type: 'number',
			min: 1,
			max: 100
		},
		'team-size': {
			type: 'number',
			min: 1,
			max: 8
		},
		'songs': {
			type: 'number',
			min: 5,
			max: 100
		},
		'watched-distribution': {
			type: 'select',
			options: [
				{ value: 'random', label: 'Random' },
				{ value: 'equal', label: 'Equal' }
			]
		},

		// Quiz Zone Settings
		// 'guess-time' - complex object with random/value/min/max, needs dialog
		// 'extra-time' - complex object with random/value/min/max, needs dialog
		// 'playback-speed' - complex object with random/value/options, needs dialog

		// New General Zone Settings - these are complex and need popout dialogs
		// 'songs-and-types' - complex, needs dialog

		// New Quiz Zone Settings - these are complex and need popout dialogs
		// 'song-difficulty' - complex, needs dialog

		// New Anime Zone Settings - these are complex and need popout dialogs
		// 'player-score' - complex, needs dialog
		// 'anime-score' - complex, needs dialog
		// 'vintage' - complex, needs dialog
		// 'genres' - complex, needs dialog
		// 'tags' - complex, needs dialog
	};

	// Check if this field supports inline editing
	// Use data.id directly if available, otherwise fall back to title-based ID
	// This ensures consistent field type detection across node recreation
	const settingId = $derived(data.id || data.title?.toLowerCase().replace(/\s+/g, '-'));
	const isSimpleField = $derived(simpleFieldConfigs[settingId] !== undefined);
	const fieldConfig = $derived(simpleFieldConfigs[settingId]);

	// Get the color with opacity for background
	const getBackgroundColor = (color) => {
		// Convert hex to rgba with low opacity for white base
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		// White background with color tint
		return `rgba(255, 255, 255, 0.95)`;
	};

	// Get the color with higher opacity for border
	const getBorderColor = (color) => {
		// Convert hex to rgba with higher opacity for brighter border
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.9)`;
	};

	// Get color tint for the card
	const getColorTint = (color) => {
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.08)`;
	};

	const backgroundColor = $derived(getBackgroundColor(data.color));
	const borderColor = $derived(getBorderColor(data.color));
	const colorTint = $derived(getColorTint(data.color));

	// Format setting value for display
	function formatSettingValue(value) {
		if (typeof value === 'object' && value !== null) {
			if (Array.isArray(value)) {
				return value.length > 0 ? `${value.length} items` : 'None';
			}

			// Handle number-with-random objects like { random: true, min: 1, max: 10 } or { random: false, value: 20 }
			if (value.random !== undefined) {
				if (value.random === true && value.min !== undefined && value.max !== undefined) {
					return `Random ${value.min}-${value.max}`;
				}
				if (value.random === false && value.value !== undefined) {
					return value.value.toString();
				}
				// Handle select-with-random objects
				if (value.random === true && value.options) {
					const enabledOptions = Object.keys(value.options).filter(key => value.options[key] === true);
					return enabledOptions.length > 0 ? `Random (${enabledOptions.length} options)` : 'Random (none)';
				}
			}

			// Handle range objects like { min: 1, max: 10 }
			if (value.min !== undefined && value.max !== undefined) {
				return `${value.min} - ${value.max}`;
			}

			// Handle sample point objects with static/range mode
			if (value.useRange !== undefined && value.staticValue !== undefined) {
				return value.useRange ? `${value.start || 1}% - ${value.end || 100}%` : `${value.staticValue}%`;
			}

			// Handle percentage objects like { start: 0, end: 100 }
			if (value.start !== undefined && value.end !== undefined) {
				return `${value.start}% - ${value.end}%`;
			}

			// Handle songs & types selection objects (merged node)
			if (value.songCount !== undefined && value.songTypes && value.songSelection) {
				const enabledTypes = Object.keys(value.songTypes).filter(key => value.songTypes[key].enabled);
				let songCountDisplay;
				if (typeof value.songCount === 'object') {
					songCountDisplay = value.songCount.random ?
						`${value.songCount.min}-${value.songCount.max}` :
						value.songCount.value.toString();
				} else {
					songCountDisplay = value.songCount.toString();
				}
				return `${songCountDisplay} songs, ${enabledTypes.length} types`;
			}

			// Handle song types & selection objects
			if (value.songTypes && value.songSelection) {
				const enabledTypes = Object.keys(value.songTypes).filter(key => value.songTypes[key].enabled);
				return `${enabledTypes.length} song types, ${value.mode}`;
			}



			// Handle song difficulty objects
			if (value.easy && value.medium && value.hard) {
				if (value.viewMode === 'advanced') {
					const rangeCount = value.ranges ? value.ranges.length : 0;
					return rangeCount > 0 ? `${rangeCount} custom ranges` : 'Advanced (no ranges)';
				} else {
					const enabled = [value.easy.enabled, value.medium.enabled, value.hard.enabled].filter(Boolean).length;
					return `${enabled}/3 difficulties, ${value.mode}`;
				}
			}

			// Handle score range objects
			if (value.mode && (value.mode === 'range' || value.mode === 'percentages')) {
				if (value.mode === 'range') {
					return `${value.min} - ${value.max}`;
				} else {
					const nonZero = Object.values(value.percentages || {}).filter(p => p > 0).length;
					return `${nonZero} scores with %`;
				}
			}

			// Handle vintage objects
			if (value.ranges && Array.isArray(value.ranges)) {
				if (value.ranges.length === 1) {
					const range = value.ranges[0];
					return `${range.from.season} ${range.from.year} - ${range.to.season} ${range.to.year}`;
				}
				return `${value.ranges.length} vintage ranges`;
			}

			// Handle genres/tags objects
			if (value.mode && (value.included !== undefined || value.excluded !== undefined)) {
				const total = (value.included?.length || 0) + (value.excluded?.length || 0) + (value.optional?.length || 0);
				return `${value.mode}: ${total} items`;
			}

			// Handle checkbox objects like { tv: true, movie: false, ... }
			if (typeof value === 'object' && !Array.isArray(value) && value.random === undefined) {
				const enabledKeys = Object.keys(value).filter(key => value[key] === true);
				if (enabledKeys.length > 0) {
					return enabledKeys.length === Object.keys(value).length ? 'All enabled' : `${enabledKeys.length} enabled`;
				}
				return 'None enabled';
			}

			return JSON.stringify(value);
		}
		if (typeof value === 'boolean') {
			return value ? 'Enabled' : 'Disabled';
		}
		if (typeof value === 'number') {
			return value.toString();
		}
		return value || 'Not set';
	}

	// Handle node click to open edit dialog (only for complex fields)
	function handleNodeClick(event) {
		// Prevent event from bubbling to SvelteFlow
		event.stopPropagation();

		// If it's a simple field and not already editing, don't open dialog
		if (isSimpleField && !isInlineEditing) {
			return;
		}

		nodeDataForDialog = {
			id: data.id || data.title?.toLowerCase().replace(/\s+/g, '-'),
			title: data.title,
			icon: data.icon,
			color: data.color,
			currentValue: currentValue
		};

		dialogOpen = true;
	}

	// Handle inline edit save
	function handleInlineSave(event) {
		const newValue = event.detail.value;
		console.log('IndividualSettingNode: handleInlineSave called with value:', newValue);

		// Update local state
		currentValue = newValue;

		// Call the parent's value change handler
		const changeData = {
			nodeId: data.id || settingId,
			newValue: newValue,
			zone: data.zone
		};
		console.log('IndividualSettingNode: calling onValueChange with:', changeData);
		onValueChange(changeData);
	}

	// Handle save from dialog
	function handleSave(saveData) {
		console.log('IndividualSettingNode: handleSave called with:', saveData);

		// Update local state
		currentValue = saveData.newValue;

		// Call the parent's value change handler
		const changeData = {
			nodeId: saveData.nodeId,
			newValue: saveData.newValue,
			zone: data.zone
		};
		console.log('IndividualSettingNode: calling onValueChange with:', changeData);
		onValueChange(changeData);
	}
</script>

{#if isSimpleField}
	<div
		class="relative w-48 transition-shadow duration-200 individual-setting-node"
		style="background: {backgroundColor}; border: 2px solid {borderColor}; border-radius: 8px; box-shadow: 0 1px 4px rgba(0,0,0,0.1);"
	>
		<!-- Output handle to connect to zone area -->
		<Handle type="source" position={Position.Bottom} style="width: 10px; height: 10px; background: {data.color}; border: 2px solid white;" />

		<div class="p-2">
			<!-- Compact header -->
			<div class="flex items-center gap-2 mb-2" style="background: {colorTint}; padding: 6px; border-radius: 6px; margin: -2px; margin-bottom: 6px;">
				<span class="text-sm">{data.icon}</span>
				<div class="flex-1 min-w-0">
					<div class="text-sm font-semibold text-gray-800 truncate">{data.title}</div>
				</div>
				<span class="text-xs text-gray-500">✏️</span>
			</div>

			<!-- Compact value display with inline editing -->
			<div class="bg-gray-50 rounded p-1.5">
				<InlineEdit
					bind:value={currentValue}
					bind:isEditing={isInlineEditing}
					type={fieldConfig.type}
					options={fieldConfig.options || []}
					min={fieldConfig.min}
					max={fieldConfig.max}
					placeholder="Not set"
					class="w-full text-xs font-medium text-gray-800"
					on:save={handleInlineSave}
				/>
			</div>
		</div>
	</div>
{:else}
	<button
		class="relative w-48 p-0 text-left transition-shadow duration-200 border-0 cursor-pointer individual-setting-node hover:shadow-md"
		style="background: {backgroundColor}; border: 2px solid {borderColor}; border-radius: 8px; box-shadow: 0 1px 4px rgba(0,0,0,0.1);"
		onclick={handleNodeClick}
	>
		<!-- Output handle to connect to zone area -->
		<Handle type="source" position={Position.Bottom} style="width: 10px; height: 10px; background: {data.color}; border: 2px solid white;" />

		<div class="p-2">
			<!-- Compact header -->
			<div class="flex items-center gap-2 mb-2" style="background: {colorTint}; padding: 6px; border-radius: 6px; margin: -2px; margin-bottom: 6px;">
				<span class="text-sm">{data.icon}</span>
				<div class="flex-1 min-w-0">
					<div class="text-sm font-semibold text-gray-800 truncate">{data.title}</div>
				</div>
				<span class="text-xs text-gray-500">⚙️</span>
			</div>

			<!-- Compact value display -->
			<div class="bg-gray-50 rounded p-1.5">
				<div class="text-xs font-medium text-gray-800 truncate">
					{formatSettingValue(currentValue)}
				</div>
			</div>
		</div>
	</button>
{/if}

<!-- Edit Dialog -->
<NodeEditDialog
	bind:open={dialogOpen}
	bind:nodeData={nodeDataForDialog}
	onSave={handleSave}
	{onModalClose}
/>

<style>
	.individual-setting-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
	
	:global(.individual-setting-node .svelte-flow__handle) {
		width: 12px;
		height: 12px;
		border: 2px solid white;
		box-shadow: 0 1px 3px rgba(0,0,0,0.1);
	}
	
	:global(.individual-setting-node .svelte-flow__handle.svelte-flow__handle-bottom) {
		bottom: -6px;
	}
</style>
