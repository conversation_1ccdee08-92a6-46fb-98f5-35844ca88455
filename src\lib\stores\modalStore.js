import { writable } from 'svelte/store';

// Store to track currently open modals/dropdowns
export const openModals = writable(new Set());

// Function to register a modal/dropdown as open
export function openModal(id) {
	openModals.update(modals => {
		const newModals = new Set();
		newModals.add(id);
		return newModals;
	});
}

// Function to close a specific modal/dropdown
export function closeModal(id) {
	openModals.update(modals => {
		const newModals = new Set(modals);
		newModals.delete(id);
		return newModals;
	});
}

// Function to close all modals/dropdowns
export function closeAllModals() {
	openModals.set(new Set());
}

// Function to check if a modal is open
export function isModalOpen(id) {
	let isOpen = false;
	openModals.subscribe(modals => {
		isOpen = modals.has(id);
	})();
	return isOpen;
}
