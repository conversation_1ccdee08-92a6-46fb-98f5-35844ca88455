<!-- SongDifficulty.svelte - Complex song difficulty form -->
<script>
	import { Label } from '$lib/components/ui/label';
	import { Switch } from '$lib/components/ui/switch';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Input } from '$lib/components/ui/input';
	import RangeSlider from 'svelte-range-slider-pips';

	let {
		editedValue = $bindable(),
		config,
		getNodeColor = () => '#6366f1',
		getTotalSongs = () => 20,
		isValid = $bindable(true),
		validationMessage = $bindable('')
	} = $props();

	// Track if we're currently updating sliders to prevent infinite loops
	let isUpdatingSliders = false;
	let isManualEdit = false;

	// Initialize view mode if not present
	if (!editedValue.viewMode) {
		editedValue.viewMode = 'basic';
	}

	// Initialize random range properties if not present
	const initializeRandomRangeProperties = () => {
		const difficulties = ['easy', 'medium', 'hard'];
		difficulties.forEach(difficulty => {
			if (editedValue[difficulty]) {
				// Initialize randomRange if not present
				if (editedValue[difficulty].randomRange === undefined) {
					editedValue[difficulty].randomRange = false;
				}
				// Initialize min/max percentage values if not present
				if (editedValue[difficulty].minPercentage === undefined) {
					editedValue[difficulty].minPercentage = 25;
				}
				if (editedValue[difficulty].maxPercentage === undefined) {
					editedValue[difficulty].maxPercentage = 40;
				}
				// Initialize min/max count values if not present
				if (editedValue[difficulty].minCount === undefined) {
					editedValue[difficulty].minCount = difficulty === 'hard' ? 4 : 5;
				}
				if (editedValue[difficulty].maxCount === undefined) {
					editedValue[difficulty].maxCount = difficulty === 'hard' ? 8 : 10;
				}
			}
		});
	};

	// Initialize properties on component mount
	initializeRandomRangeProperties();

	// Initialize ranges if not present
	if (!editedValue.ranges) {
		editedValue.ranges = [];
	}

	// Get enabled difficulty types
	function getEnabledTypes() {
		return Object.keys(editedValue).filter(type => type !== 'mode' && type !== 'viewMode' && type !== 'ranges' && editedValue[type] && editedValue[type].enabled);
	}

	// Advanced view functions
	function addRange() {
		editedValue.ranges = [...editedValue.ranges, { from: 10, to: 20 }];
		validateRanges();
	}

	function removeRange(index) {
		editedValue.ranges = editedValue.ranges.filter((_, i) => i !== index);
		validateRanges();
	}

	function updateRange(index, field, value) {
		const numValue = parseInt(value) || 0;
		const clampedValue = Math.max(0, Math.min(100, numValue));

		editedValue.ranges[index][field] = clampedValue;

		// Ensure from <= to
		if (field === 'from' && clampedValue > editedValue.ranges[index].to) {
			editedValue.ranges[index].to = clampedValue;
		} else if (field === 'to' && clampedValue < editedValue.ranges[index].from) {
			editedValue.ranges[index].from = clampedValue;
		}

		validateRanges();
	}

	// Handle range slider changes
	function handleRangeSliderChange(index, event) {
		const values = event.detail.values;
		editedValue.ranges[index].from = values[0];
		editedValue.ranges[index].to = values[1];
		validateRanges();
	}

	function validateRanges() {
		if (editedValue.viewMode !== 'advanced') {
			isValid = true;
			validationMessage = '';
			return;
		}

		if (editedValue.ranges.length === 0) {
			isValid = false;
			validationMessage = 'At least one difficulty range is required in advanced mode.';
			return;
		}

		// Check for overlapping ranges
		for (let i = 0; i < editedValue.ranges.length; i++) {
			for (let j = i + 1; j < editedValue.ranges.length; j++) {
				const range1 = editedValue.ranges[i];
				const range2 = editedValue.ranges[j];

				if ((range1.from <= range2.to && range1.to >= range2.from)) {
					isValid = false;
					validationMessage = `Ranges ${i + 1} and ${j + 1} overlap. Please adjust the ranges.`;
					return;
				}
			}
		}

		isValid = true;
		validationMessage = '';
	}



	// Validate total when manual editing is complete
	function validateAndFixTotal() {
		if (isUpdatingSliders) return;

		// For advanced mode, use range validation
		if (editedValue.viewMode === 'advanced') {
			validateRanges();
			return;
		}

		// For basic mode, use the original validation
		const enabledTypes = getEnabledTypes();
		if (enabledTypes.length === 0) {
			isValid = true;
			validationMessage = '';
			return;
		}

		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const currentTotal = enabledTypes.reduce((sum, type) =>
			sum + editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'], 0);

		if (Math.abs(currentTotal - maxValue) <= 0.01) { // Allow for tiny rounding errors
			isValid = true;
			validationMessage = '';
		} else {
			isValid = false;
			validationMessage = `Song difficulties must total ${maxValue}${editedValue.mode === 'percentage' ? '%' : ' songs'}. Current total: ${currentTotal}${editedValue.mode === 'percentage' ? '%' : ' songs'}`;
		}
	}

	// Handle slider changes - ensure values always total correctly
	function handleSliderChange(type, event) {
		const newValue = event.detail.value;

		// Prevent validation during slider updates
		isUpdatingSliders = true;

		const enabledTypes = getEnabledTypes();
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const otherTypes = enabledTypes.filter(t => t !== type);

		// Set the new value for the changed slider
		editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = newValue;

		if (otherTypes.length === 0) {
			// Only one slider enabled, no adjustment needed
			isUpdatingSliders = false;
			isValid = true;
			validationMessage = '';
			return;
		}

		// Calculate remaining value to distribute
		const remaining = maxValue - newValue;

		if (remaining < 0) {
			// If new value exceeds max, set it to max and others to 0
			editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = maxValue;
			otherTypes.forEach(t => {
				editedValue[t][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = 0;
			});
		} else if (remaining === 0) {
			// If new value equals max, set others to 0
			otherTypes.forEach(t => {
				editedValue[t][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = 0;
			});
		} else {
			// Distribute remaining value proportionally among other types
			const currentOtherTotal = otherTypes.reduce((sum, t) =>
				sum + editedValue[t][editedValue.mode === 'percentage' ? 'percentage' : 'count'], 0);

			if (currentOtherTotal > 0) {
				// Proportional distribution
				let distributedTotal = 0;

				// Distribute to all but the last slider
				for (let i = 0; i < otherTypes.length - 1; i++) {
					const t = otherTypes[i];
					const currentValue = editedValue[t][editedValue.mode === 'percentage' ? 'percentage' : 'count'];
					const proportion = currentValue / currentOtherTotal;
					const newVal = Math.round(remaining * proportion);
					editedValue[t][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = newVal;
					distributedTotal += newVal;
				}

				// Set the last slider to ensure exact total
				const lastType = otherTypes[otherTypes.length - 1];
				editedValue[lastType][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = remaining - distributedTotal;
			} else {
				// If all other values are 0, distribute equally
				const equalShare = Math.floor(remaining / otherTypes.length);
				const remainder = remaining % otherTypes.length;

				otherTypes.forEach((t, index) => {
					const value = equalShare + (index < remainder ? 1 : 0);
					editedValue[t][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = value;
				});
			}
		}

		// Re-enable validation and validate
		isUpdatingSliders = false;
		validateAndFixTotal();
	}

	// Handle input changes (manual editing - no auto-linking)
	function handleInputChange(type, event) {
		isManualEdit = true;
		const newValue = parseInt(event.target.value) || 0;
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();

		// Clamp value to valid range
		const clampedValue = Math.max(0, Math.min(maxValue, newValue));
		editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = clampedValue;

		// Only validate, don't auto-adjust other sliders
		validateAndFixTotal();
	}

	// Handle input blur (when user finishes editing)
	function handleInputBlur(type) {
		if (!isManualEdit) return;
		isManualEdit = false;

		// Only validate, don't auto-adjust
		validateAndFixTotal();
	}

	// Quick fix function for song difficulties only
	function quickFixValues() {
		const enabledTypes = getEnabledTypes();
		if (enabledTypes.length === 0) return;

		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const currentTotal = enabledTypes.reduce((sum, type) =>
			sum + editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'], 0);

		if (currentTotal === maxValue) return; // Already correct

		// Proportionally adjust values to sum to maxValue
		if (currentTotal > 0) {
			const scaleFactor = maxValue / currentTotal;
			let adjustedTotal = 0;

			// Apply scaling and track total
			enabledTypes.forEach((type, index) => {
				const currentValue = editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'];
				const scaledValue = Math.round(currentValue * scaleFactor);
				editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = scaledValue;
				adjustedTotal += scaledValue;
			});

			// Handle rounding errors by adjusting the largest value
			const difference = maxValue - adjustedTotal;
			if (difference !== 0) {
				// Find the type with the largest value to adjust
				const largestType = enabledTypes.reduce((max, type) => {
					const currentValue = editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'];
					const maxValue = editedValue[max][editedValue.mode === 'percentage' ? 'percentage' : 'count'];
					return currentValue > maxValue ? type : max;
				});

				editedValue[largestType][editedValue.mode === 'percentage' ? 'percentage' : 'count'] += difference;
			}
		} else {
			// If all values are 0, distribute equally
			const equalShare = Math.floor(maxValue / enabledTypes.length);
			const remainder = maxValue % enabledTypes.length;

			enabledTypes.forEach((type, index) => {
				const value = equalShare + (index < remainder ? 1 : 0);
				editedValue[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = value;
			});
		}

		validateAndFixTotal();
	}

	// Convert values when mode changes
	let previousMode = editedValue.mode;
	$effect(() => {
		if (previousMode !== editedValue.mode) {
			convertValuesForModeChange(previousMode, editedValue.mode);
			previousMode = editedValue.mode;
		}
	});

	// Convert values between percentage and count modes
	function convertValuesForModeChange(fromMode, toMode) {
		const totalSongs = getTotalSongs();

		// Convert difficulty types
		const enabledTypes = getEnabledTypes();
		enabledTypes.forEach(type => {
			if (fromMode === 'percentage' && toMode === 'count') {
				// Convert percentage to count
				const percentage = editedValue[type].percentage;
				editedValue[type].count = Math.round((percentage / 100) * totalSongs);
			} else if (fromMode === 'count' && toMode === 'percentage') {
				// Convert count to percentage
				const count = editedValue[type].count;
				editedValue[type].percentage = Math.round((count / totalSongs) * 100);
			}
		});

		// Ensure totals are correct after conversion
		setTimeout(() => {
			const enabledTypes = getEnabledTypes();
			if (enabledTypes.length > 0) {
				const maxValue = toMode === 'percentage' ? 100 : totalSongs;

				// Fix difficulty types total
				const currentTotal = enabledTypes.reduce((sum, type) =>
					sum + editedValue[type][toMode === 'percentage' ? 'percentage' : 'count'], 0);

				if (currentTotal !== maxValue && currentTotal > 0) {
					const scaleFactor = maxValue / currentTotal;
					let distributedTotal = 0;

					// Scale all but the last value
					for (let i = 0; i < enabledTypes.length - 1; i++) {
						const type = enabledTypes[i];
						const currentValue = editedValue[type][toMode === 'percentage' ? 'percentage' : 'count'];
						const newValue = Math.round(currentValue * scaleFactor);
						editedValue[type][toMode === 'percentage' ? 'percentage' : 'count'] = newValue;
						distributedTotal += newValue;
					}

					// Set the last value to ensure exact total
					const lastType = enabledTypes[enabledTypes.length - 1];
					editedValue[lastType][toMode === 'percentage' ? 'percentage' : 'count'] = maxValue - distributedTotal;
				}
			}

			validateAndFixTotal();
		}, 0);
	}

	// Watch for checkbox changes to redistribute values
	$effect(() => {
		const enabledTypes = getEnabledTypes();
		if (enabledTypes.length === 0) return;

		// If only one type is enabled, give it the full value
		if (enabledTypes.length === 1) {
			const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
			editedValue[enabledTypes[0]][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = maxValue;
		}

		validateAndFixTotal();
	});

	// Watch for advanced mode changes and force percentage mode
	$effect(() => {
		if (editedValue.viewMode === 'advanced' && editedValue.mode !== 'percentage') {
			editedValue.mode = 'percentage';
		}
	});
</script>

<div class="space-y-4">

	<!-- Content with Side Controls -->
	<div class="flex gap-4">
		<!-- Main Content -->
		<div class="flex-1">
			{#if editedValue.viewMode === 'basic'}
				<!-- Basic View -->
				<div class="p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
					<div class="space-y-4">
			<!-- Easy -->
			<div class="space-y-2">
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.easy.enabled} id="easy" />
						<Label for="easy" class="text-sm">Easy</Label>
					</div>
					{#if editedValue.easy.enabled}
						<div class="flex items-center space-x-2">
							<Checkbox bind:checked={editedValue.easy.randomRange} id="easy-range" />
							<Label for="easy-range" class="text-xs text-gray-600">Random range</Label>
						</div>
					{/if}
				</div>
				{#if editedValue.easy.enabled}
					{#if editedValue.easy.randomRange}
						<!-- Random Range Mode -->
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.easy.minPercentage, editedValue.easy.maxPercentage]}
									min={0} max={100} step={1} range pushy pips pipstep={25} all="label"
									on:change={(e) => {
										editedValue.easy.minPercentage = e.detail.values[0];
										editedValue.easy.maxPercentage = e.detail.values[1];
										validateAndFixTotal();
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.easy.minPercentage}
											min={0} max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.easy.minPercentage = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
									<span class="text-xs text-gray-400">to</span>
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.easy.maxPercentage}
											min={0} max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.easy.maxPercentage = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.easy.minCount, editedValue.easy.maxCount]}
									min={0} max={getTotalSongs()} step={1} range pushy pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => {
										editedValue.easy.minCount = e.detail.values[0];
										editedValue.easy.maxCount = e.detail.values[1];
										validateAndFixTotal();
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.easy.minCount}
											min={0} max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.easy.minCount = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
									<span class="text-xs text-gray-400">to</span>
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.easy.maxCount}
											min={0} max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.easy.maxCount = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{:else}
						<!-- Single Value Mode -->
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.easy.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => handleSliderChange('easy', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.easy.percentage}
											min={0}
											max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('easy', e)}
											onblur={() => handleInputBlur('easy')}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.easy.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => handleSliderChange('easy', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.easy.count}
											min={0}
											max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('easy', e)}
											onblur={() => handleInputBlur('easy')}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{/if}
				{/if}
			</div>

			<!-- Medium -->
			<div class="space-y-2">
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.medium.enabled} id="medium" />
						<Label for="medium" class="text-sm">Medium</Label>
					</div>
					{#if editedValue.medium.enabled}
						<div class="flex items-center space-x-2">
							<Checkbox bind:checked={editedValue.medium.randomRange} id="medium-range" />
							<Label for="medium-range" class="text-xs text-gray-600">Random range</Label>
						</div>
					{/if}
				</div>
				{#if editedValue.medium.enabled}
					{#if editedValue.medium.randomRange}
						<!-- Random Range Mode -->
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.medium.minPercentage, editedValue.medium.maxPercentage]}
									min={0} max={100} step={1} range pushy pips pipstep={25} all="label"
									on:change={(e) => {
										editedValue.medium.minPercentage = e.detail.values[0];
										editedValue.medium.maxPercentage = e.detail.values[1];
										validateAndFixTotal();
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.medium.minPercentage}
											min={0} max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.medium.minPercentage = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
									<span class="text-xs text-gray-400">to</span>
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.medium.maxPercentage}
											min={0} max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.medium.maxPercentage = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.medium.minCount, editedValue.medium.maxCount]}
									min={0} max={getTotalSongs()} step={1} range pushy pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => {
										editedValue.medium.minCount = e.detail.values[0];
										editedValue.medium.maxCount = e.detail.values[1];
										validateAndFixTotal();
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.medium.minCount}
											min={0} max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.medium.minCount = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
									<span class="text-xs text-gray-400">to</span>
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.medium.maxCount}
											min={0} max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.medium.maxCount = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{:else}
						<!-- Single Value Mode -->
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.medium.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => handleSliderChange('medium', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.medium.percentage}
											min={0}
											max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('medium', e)}
											onblur={() => handleInputBlur('medium')}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.medium.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => handleSliderChange('medium', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.medium.count}
											min={0}
											max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('medium', e)}
											onblur={() => handleInputBlur('medium')}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{/if}
				{/if}
			</div>

			<!-- Hard -->
			<div class="space-y-2">
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.hard.enabled} id="hard" />
						<Label for="hard" class="text-sm">Hard</Label>
					</div>
					{#if editedValue.hard.enabled}
						<div class="flex items-center space-x-2">
							<Checkbox bind:checked={editedValue.hard.randomRange} id="hard-range" />
							<Label for="hard-range" class="text-xs text-gray-600">Random range</Label>
						</div>
					{/if}
				</div>
				{#if editedValue.hard.enabled}
					{#if editedValue.hard.randomRange}
						<!-- Random Range Mode -->
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.hard.minPercentage, editedValue.hard.maxPercentage]}
									min={0} max={100} step={1} range pushy pips pipstep={25} all="label"
									on:change={(e) => {
										editedValue.hard.minPercentage = e.detail.values[0];
										editedValue.hard.maxPercentage = e.detail.values[1];
										validateAndFixTotal();
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.hard.minPercentage}
											min={0} max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.hard.minPercentage = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
									<span class="text-xs text-gray-400">to</span>
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.hard.maxPercentage}
											min={0} max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.hard.maxPercentage = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.hard.minCount, editedValue.hard.maxCount]}
									min={0} max={getTotalSongs()} step={1} range pushy pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => {
										editedValue.hard.minCount = e.detail.values[0];
										editedValue.hard.maxCount = e.detail.values[1];
										validateAndFixTotal();
									}}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center space-x-4">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.hard.minCount}
											min={0} max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.hard.minCount = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
									<span class="text-xs text-gray-400">to</span>
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.hard.maxCount}
											min={0} max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => {
												editedValue.hard.maxCount = parseInt(e.target.value) || 0;
												validateAndFixTotal();
											}}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{:else}
						<!-- Single Value Mode -->
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.hard.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => handleSliderChange('hard', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.hard.percentage}
											min={0}
											max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('hard', e)}
											onblur={() => handleInputBlur('hard')}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.hard.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => handleSliderChange('hard', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.hard.count}
											min={0}
											max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('hard', e)}
											onblur={() => handleInputBlur('hard')}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{/if}
				{/if}
				</div>

				{#if !isValid}
					<div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
						<p class="text-sm text-red-600 mb-2">{validationMessage}</p>
						<button
							type="button"
							onclick={quickFixValues}
							class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
						>
							Quick Fix
						</button>
					</div>
				{/if}
						</div> <!-- Close space-y-4 -->
					</div> <!-- Close p-4 bg-white -->
				{:else}
			<!-- Advanced View -->
			<div class="p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
		<div class="flex items-center justify-between mb-4">
			<Label class="text-base font-semibold text-gray-800">Percentage Ranges</Label>
			<button
				type="button"
				onclick={addRange}
				class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center gap-1"
			>
				<span>+</span> Add Range
			</button>
		</div>

		{#if editedValue.ranges.length === 0}
			<div class="text-center py-6 text-gray-500">
				<p class="text-base mb-1">No ranges defined</p>
				<p class="text-sm">Click "Add Range" to create your first percentage range</p>
			</div>
		{:else}
			<div class="space-y-3">
				{#each editedValue.ranges as range, index}
					<div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
						<div class="flex items-center justify-between mb-3">
							<Label class="text-sm font-medium text-gray-700">Range {index + 1}</Label>
							<button
								type="button"
								onclick={() => removeRange(index)}
								class="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
							>
								Remove
							</button>
						</div>

						<!-- Range Slider -->
						<div class="mb-3">
							<Label class="text-xs text-gray-600 mb-2 block">Difficulty Range (%)</Label>
							<div class="px-2">
								<RangeSlider
									values={[range.from, range.to]}
									min={0}
									max={100}
									step={1}
									range={true}
									pips
									pipstep={25}
									all="label"
									on:change={(e) => handleRangeSliderChange(index, e)}
									--slider={getNodeColor()}
									--handle={getNodeColor()}
									--range={getNodeColor()}
									--progress={getNodeColor()}
								/>
							</div>
						</div>

						<!-- Manual Input Fields -->
						<div class="grid grid-cols-2 gap-3 mb-3">
							<div>
								<Label for="from-{index}" class="text-xs text-gray-600">From %</Label>
								<Input
									id="from-{index}"
									type="number"
									value={range.from}
									min={0}
									max={100}
									class="w-full text-sm"
									oninput={(e) => updateRange(index, 'from', e.target.value)}
								/>
							</div>
							<div>
								<Label for="to-{index}" class="text-xs text-gray-600">To %</Label>
								<Input
									id="to-{index}"
									type="number"
									value={range.to}
									min={0}
									max={100}
									class="w-full text-sm"
									oninput={(e) => updateRange(index, 'to', e.target.value)}
								/>
							</div>
						</div>

						<!-- Range Preview -->
						<div class="p-2 bg-white border rounded text-center">
							<span class="font-medium" style="color: {getNodeColor()}">{range.from}% - {range.to}%</span>
							<span class="text-sm text-gray-600 ml-2">({range.to - range.from}% range)</span>
						</div>
					</div>
				{/each}
			</div>
		{/if}

			{#if !isValid}
				<div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
					<p class="text-sm text-red-600">{validationMessage}</p>
				</div>
			{/if}
				</div>
			{/if}
		</div>

		<!-- Side Controls -->
		<div class="w-64 flex-shrink-0">
			<div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm sticky top-4">
				<!-- Mode Toggle -->
				<div class="mb-4">
					<Label class="text-sm font-medium text-gray-700 mb-2 block">Mode</Label>
					<div class="flex items-center space-x-2">
						<Label for="mode-switch" class="text-sm {editedValue.mode === 'percentage' ? 'font-semibold text-blue-600' : 'text-gray-500'}">%</Label>
						<Switch
							id="mode-switch"
							checked={editedValue.mode === 'count'}
							disabled={editedValue.viewMode === 'advanced'}
							onCheckedChange={(checked) => editedValue.mode = checked ? 'count' : 'percentage'}
						/>
						<Label for="mode-switch" class="text-sm {editedValue.mode === 'count' ? 'font-semibold text-blue-600' : 'text-gray-500'}">Count</Label>
					</div>
					{#if editedValue.viewMode === 'advanced'}
						<div class="text-xs text-orange-600 mt-1">
							% mode required for advanced ranges
						</div>
					{/if}
				</div>

				<!-- View Toggle -->
				<div class="mb-4">
					<Label class="text-sm font-medium text-gray-700 mb-2 block">View</Label>
					<div class="flex items-center space-x-2">
						<Label for="view-switch" class="text-sm {editedValue.viewMode === 'basic' ? 'font-semibold text-blue-600' : 'text-gray-500'}">Basic</Label>
						<Switch
							id="view-switch"
							checked={editedValue.viewMode === 'advanced'}
							onCheckedChange={(checked) => {
								editedValue.viewMode = checked ? 'advanced' : 'basic';
								validateAndFixTotal();
							}}
						/>
						<Label for="view-switch" class="text-sm {editedValue.viewMode === 'advanced' ? 'font-semibold text-blue-600' : 'text-gray-500'}">Advanced</Label>
					</div>

					<!-- Info text explaining the difference between modes -->
					<div class="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
						{#if editedValue.viewMode === 'advanced'}
							<strong>Advanced View:</strong> Percentages apply to the song guess rate (how likely each difficulty is to be guessed correctly).
						{:else}
							<strong>Basic View:</strong> Percentages apply to how many songs from each difficulty category will play in the game.
						{/if}
					</div>
				</div>

				<!-- Info -->
				<div class="text-xs text-gray-500 pt-2 border-t">
					<div>Songs: {getTotalSongs()}</div>
					<div class="mt-1">
						{#if editedValue.viewMode === 'basic'}
							Simple difficulty distribution
						{:else}
							Custom percentage ranges
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>
	</div>
