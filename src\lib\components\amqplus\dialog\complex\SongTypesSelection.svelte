<!-- SongTypesSelection.svelte - Complex song types and selection form -->
<script>
	import { Label } from '$lib/components/ui/label';
	import { Switch } from '$lib/components/ui/switch';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Input } from '$lib/components/ui/input';
	import RangeSlider from 'svelte-range-slider-pips';

	let {
		editedValue = $bindable(),
		config,
		getNodeColor = () => '#6366f1',
		getTotalSongs = () => 20,
		isValid = $bindable(true),
		validationMessage = $bindable('')
	} = $props();

	// Track if we're currently updating sliders to prevent infinite loops
	let isUpdatingSliders = false;
	let isManualEdit = false;

	// Get enabled song types
	function getEnabledTypes() {
		return Object.keys(editedValue.songTypes).filter(type => editedValue.songTypes[type].enabled);
	}

	// Update linked sliders when one changes
	function updateLinkedSliders(changedType, newValue, isFromSlider = true) {
		if (isUpdatingSliders) return;
		isUpdatingSliders = true;

		const enabledTypes = getEnabledTypes();
		if (enabledTypes.length <= 1) {
			isUpdatingSliders = false;
			return;
		}

		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const otherTypes = enabledTypes.filter(type => type !== changedType);

		// Calculate remaining value to distribute
		const remaining = maxValue - newValue;

		if (remaining < 0) {
			// If new value exceeds max, set it to max and others to 0
			editedValue.songTypes[changedType][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = maxValue;
			otherTypes.forEach(type => {
				editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = 0;
			});
		} else if (remaining === 0) {
			// If new value equals max, set others to 0
			otherTypes.forEach(type => {
				editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = 0;
			});
		} else {
			// Distribute remaining value proportionally among other types
			const currentOtherTotal = otherTypes.reduce((sum, type) =>
				sum + editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'], 0);

			if (currentOtherTotal > 0) {
				// Proportional distribution with exact total guarantee
				let distributedTotal = 0;
				const proportions = otherTypes.map(type => {
					const currentValue = editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'];
					return currentValue / currentOtherTotal;
				});

				// Distribute all but the last value using proportions
				for (let i = 0; i < otherTypes.length - 1; i++) {
					const type = otherTypes[i];
					const newOtherValue = Math.round(remaining * proportions[i]);
					editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = newOtherValue;
					distributedTotal += newOtherValue;
				}

				// Set the last value to ensure exact total
				const lastType = otherTypes[otherTypes.length - 1];
				editedValue.songTypes[lastType][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = remaining - distributedTotal;
			} else {
				// Equal distribution if all others are 0
				const equalShare = Math.floor(remaining / otherTypes.length);
				const remainder = remaining % otherTypes.length;

				otherTypes.forEach((type, index) => {
					const value = equalShare + (index < remainder ? 1 : 0);
					editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = value;
				});
			}
		}

		isUpdatingSliders = false;
	}

	// Validate total when manual editing is complete
	function validateAndFixTotal() {
		if (isUpdatingSliders) return;

		const enabledTypes = getEnabledTypes();
		let songTypesValid = true;
		let songSelectionValid = true;
		let errorMessages = [];

		// Validate song types
		if (enabledTypes.length > 0) {
			const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
			const currentTotal = enabledTypes.reduce((sum, type) =>
				sum + editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'], 0);

			if (currentTotal !== maxValue) {
				songTypesValid = false;
				errorMessages.push(`Song types must total ${maxValue}${editedValue.mode === 'percentage' ? '%' : ' songs'}. Current total: ${currentTotal}${editedValue.mode === 'percentage' ? '%' : ' songs'}`);
			}
		}

		// Validate song selection (respects mode)
		const maxSelectionValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const selectionTotal = editedValue.songSelection.random + editedValue.songSelection.watched;
		if (selectionTotal !== maxSelectionValue) {
			songSelectionValid = false;
			const unit = editedValue.mode === 'percentage' ? '%' : ' songs';
			errorMessages.push(`Song selection must total ${maxSelectionValue}${unit}. Current total: ${selectionTotal}${unit}`);
		}

		isValid = songTypesValid && songSelectionValid;
		validationMessage = errorMessages.join(' ');
	}

	// Handle slider changes
	function handleSliderChange(type, event) {
		const newValue = event.detail.value;
		editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = newValue;
		updateLinkedSliders(type, newValue, true);
		validateAndFixTotal();
	}

	// Handle input changes (manual editing - no auto-linking)
	function handleInputChange(type, event) {
		isManualEdit = true;
		const newValue = parseInt(event.target.value) || 0;
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();

		// Clamp value to valid range
		const clampedValue = Math.max(0, Math.min(maxValue, newValue));
		editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = clampedValue;

		// Only validate, don't auto-adjust other sliders
		validateAndFixTotal();
	}

	// Handle input blur (when user finishes editing)
	function handleInputBlur(type) {
		if (!isManualEdit) return;
		isManualEdit = false;

		// Only validate, don't auto-adjust
		validateAndFixTotal();
	}

	// Handle song selection slider changes (linked)
	function handleSongSelectionSliderChange(type, event) {
		const newValue = event.detail.value;
		editedValue.songSelection[type] = newValue;
		updateLinkedSongSelection(type, newValue);
		validateAndFixTotal();
	}

	// Update linked song selection sliders
	function updateLinkedSongSelection(changedType, newValue) {
		if (isUpdatingSliders) return;
		isUpdatingSliders = true;

		const otherType = changedType === 'random' ? 'watched' : 'random';
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const remaining = maxValue - newValue;

		// Clamp remaining value to valid range
		editedValue.songSelection[otherType] = Math.max(0, Math.min(maxValue, remaining));

		isUpdatingSliders = false;
	}

	// Handle song selection input changes (manual editing - no auto-linking)
	function handleSongSelectionInputChange(type, event) {
		const newValue = parseInt(event.target.value) || 0;
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const clampedValue = Math.max(0, Math.min(maxValue, newValue));
		editedValue.songSelection[type] = clampedValue;
		validateAndFixTotal();
	}

	// Quick fix function for song types only
	function quickFixSongTypes() {
		const enabledTypes = getEnabledTypes();

		if (enabledTypes.length > 0) {
			const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
			const currentTotal = enabledTypes.reduce((sum, type) =>
				sum + editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'], 0);

			if (currentTotal !== maxValue) {
				// Proportionally adjust values to sum to maxValue
				if (currentTotal > 0) {
					const scaleFactor = maxValue / currentTotal;
					let adjustedTotal = 0;

					// Apply scaling and track total
					enabledTypes.forEach((type, index) => {
						const currentValue = editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'];
						const scaledValue = Math.round(currentValue * scaleFactor);
						editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = scaledValue;
						adjustedTotal += scaledValue;
					});

					// Handle rounding errors by adjusting the largest value
					const difference = maxValue - adjustedTotal;
					if (difference !== 0) {
						// Find the type with the largest value to adjust
						const largestType = enabledTypes.reduce((max, type) => {
							const currentValue = editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'];
							const maxValue = editedValue.songTypes[max][editedValue.mode === 'percentage' ? 'percentage' : 'count'];
							return currentValue > maxValue ? type : max;
						});

						editedValue.songTypes[largestType][editedValue.mode === 'percentage' ? 'percentage' : 'count'] += difference;
					}
				} else {
					// If all values are 0, distribute equally
					const equalShare = Math.floor(maxValue / enabledTypes.length);
					const remainder = maxValue % enabledTypes.length;

					enabledTypes.forEach((type, index) => {
						const value = equalShare + (index < remainder ? 1 : 0);
						editedValue.songTypes[type][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = value;
					});
				}
			}
		}

		validateAndFixTotal();
	}

	// Quick fix function for song selection only
	function quickFixSongSelection() {
		const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
		const selectionTotal = editedValue.songSelection.random + editedValue.songSelection.watched;
		if (selectionTotal !== maxValue) {
			if (selectionTotal > 0) {
				// Proportionally adjust to sum to maxValue
				const scaleFactor = maxValue / selectionTotal;
				const adjustedRandom = Math.round(editedValue.songSelection.random * scaleFactor);
				const adjustedWatched = maxValue - adjustedRandom; // Ensure exact total

				editedValue.songSelection.random = adjustedRandom;
				editedValue.songSelection.watched = adjustedWatched;
			} else {
				// If both are 0, set to equal split
				const halfValue = Math.floor(maxValue / 2);
				editedValue.songSelection.random = halfValue;
				editedValue.songSelection.watched = maxValue - halfValue;
			}
		}

		validateAndFixTotal();
	}

	// Quick fix function to snap all values to nearest correct settings
	function quickFixValues() {
		quickFixSongTypes();
		quickFixSongSelection();
	}

	// Convert values when mode changes
	let previousMode = editedValue.mode;
	$effect(() => {
		if (previousMode !== editedValue.mode) {
			convertValuesForModeChange(previousMode, editedValue.mode);
			previousMode = editedValue.mode;
		}
	});

	// Convert values between percentage and count modes
	function convertValuesForModeChange(fromMode, toMode) {
		const totalSongs = getTotalSongs();

		// Convert song types
		const enabledTypes = getEnabledTypes();
		enabledTypes.forEach(type => {
			if (fromMode === 'percentage' && toMode === 'count') {
				// Convert percentage to count
				const percentage = editedValue.songTypes[type].percentage;
				editedValue.songTypes[type].count = Math.round((percentage / 100) * totalSongs);
			} else if (fromMode === 'count' && toMode === 'percentage') {
				// Convert count to percentage
				const count = editedValue.songTypes[type].count;
				editedValue.songTypes[type].percentage = Math.round((count / totalSongs) * 100);
			}
		});

		// Convert song selection
		if (fromMode === 'percentage' && toMode === 'count') {
			// Convert percentage to count
			editedValue.songSelection.random = Math.round((editedValue.songSelection.random / 100) * totalSongs);
			editedValue.songSelection.watched = Math.round((editedValue.songSelection.watched / 100) * totalSongs);
		} else if (fromMode === 'count' && toMode === 'percentage') {
			// Convert count to percentage
			editedValue.songSelection.random = Math.round((editedValue.songSelection.random / totalSongs) * 100);
			editedValue.songSelection.watched = Math.round((editedValue.songSelection.watched / totalSongs) * 100);
		}

		// Ensure totals are correct after conversion
		setTimeout(() => {
			const enabledTypes = getEnabledTypes();
			if (enabledTypes.length > 0) {
				const maxValue = toMode === 'percentage' ? 100 : totalSongs;

				// Fix song types total
				const currentSongTypesTotal = enabledTypes.reduce((sum, type) =>
					sum + editedValue.songTypes[type][toMode === 'percentage' ? 'percentage' : 'count'], 0);

				if (currentSongTypesTotal !== maxValue && currentSongTypesTotal > 0) {
					const scaleFactor = maxValue / currentSongTypesTotal;
					let distributedTotal = 0;

					// Scale all but the last value
					for (let i = 0; i < enabledTypes.length - 1; i++) {
						const type = enabledTypes[i];
						const currentValue = editedValue.songTypes[type][toMode === 'percentage' ? 'percentage' : 'count'];
						const newValue = Math.round(currentValue * scaleFactor);
						editedValue.songTypes[type][toMode === 'percentage' ? 'percentage' : 'count'] = newValue;
						distributedTotal += newValue;
					}

					// Set the last value to ensure exact total
					const lastType = enabledTypes[enabledTypes.length - 1];
					editedValue.songTypes[lastType][toMode === 'percentage' ? 'percentage' : 'count'] = maxValue - distributedTotal;
				}

				// Fix song selection total
				const currentSelectionTotal = editedValue.songSelection.random + editedValue.songSelection.watched;
				if (currentSelectionTotal !== maxValue && currentSelectionTotal > 0) {
					const scaleFactor = maxValue / currentSelectionTotal;
					const adjustedRandom = Math.round(editedValue.songSelection.random * scaleFactor);
					editedValue.songSelection.watched = maxValue - adjustedRandom;
					editedValue.songSelection.random = adjustedRandom;
				}
			}

			validateAndFixTotal();
		}, 0);
	}

	// Watch for checkbox changes to redistribute values
	$effect(() => {
		const enabledTypes = getEnabledTypes();
		if (enabledTypes.length === 0) return;

		// If only one type is enabled, give it the full value
		if (enabledTypes.length === 1) {
			const maxValue = editedValue.mode === 'percentage' ? 100 : getTotalSongs();
			editedValue.songTypes[enabledTypes[0]][editedValue.mode === 'percentage' ? 'percentage' : 'count'] = maxValue;
		}

		validateAndFixTotal();
	});
</script>

<div class="space-y-4">

	<!-- Mode Switch -->
	<div class="flex items-center justify-center p-3 space-x-4 bg-white border border-gray-200 rounded-lg shadow-sm">
		<Label class="text-sm font-medium text-gray-700">Mode:</Label>
		<div class="flex items-center space-x-3">
			<Label for="mode-switch" class="text-sm {editedValue.mode === 'percentage' ? 'font-semibold text-blue-600' : 'text-gray-500'}">%</Label>
			<Switch
				id="mode-switch"
				checked={editedValue.mode === 'count'}
				onCheckedChange={(checked) => editedValue.mode = checked ? 'count' : 'percentage'}
			/>
			<Label for="mode-switch" class="text-sm {editedValue.mode === 'count' ? 'font-semibold text-blue-600' : 'text-gray-500'}">Count</Label>
		</div>
		<div class="text-xs text-gray-500">
			Songs: {getTotalSongs()}
		</div>
	</div>

	<div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
		<!-- Song Types Section -->
		<div class="p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
			<Label class="block mb-3 text-base font-semibold text-gray-800">Song Types</Label>
			<div class="space-y-4">
				<!-- Openings -->
				<div class="space-y-2">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.songTypes.openings.enabled} id="openings" />
						<Label for="openings" class="text-sm">Openings</Label>
					</div>
					{#if editedValue.songTypes.openings.enabled}
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.openings.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => handleSliderChange('openings', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songTypes.openings.percentage}
											min={0}
											max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('openings', e)}
											onblur={() => handleInputBlur('openings')}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.openings.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => handleSliderChange('openings', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songTypes.openings.count}
											min={0}
											max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('openings', e)}
											onblur={() => handleInputBlur('openings')}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{/if}
				</div>

				<!-- Endings -->
				<div class="space-y-2">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.songTypes.endings.enabled} id="endings" />
						<Label for="endings" class="text-sm">Endings</Label>
					</div>
					{#if editedValue.songTypes.endings.enabled}
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.endings.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => handleSliderChange('endings', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songTypes.endings.percentage}
											min={0}
											max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('endings', e)}
											onblur={() => handleInputBlur('endings')}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.endings.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => handleSliderChange('endings', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songTypes.endings.count}
											min={0}
											max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('endings', e)}
											onblur={() => handleInputBlur('endings')}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{/if}
				</div>

				<!-- Inserts -->
				<div class="space-y-2">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.songTypes.inserts.enabled} id="inserts" />
						<Label for="inserts" class="text-sm">Inserts</Label>
					</div>
					{#if editedValue.songTypes.inserts.enabled}
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.inserts.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => handleSliderChange('inserts', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songTypes.inserts.percentage}
											min={0}
											max={100}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('inserts', e)}
											onblur={() => handleInputBlur('inserts')}
										/>
										<span class="text-xs text-gray-600">%</span>
									</div>
								</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.inserts.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => handleSliderChange('inserts', e)}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 flex justify-center">
									<div class="flex items-center space-x-1">
										<Input
											type="number"
											value={editedValue.songTypes.inserts.count}
											min={0}
											max={getTotalSongs()}
											class="w-16 h-6 text-xs text-center px-1"
											oninput={(e) => handleInputChange('inserts', e)}
											onblur={() => handleInputBlur('inserts')}
										/>
										<span class="text-xs text-gray-600">songs</span>
									</div>
								</div>
							</div>
						{/if}
					{/if}
				</div>
			</div>

			{#if !isValid}
				<div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
					<p class="text-sm text-red-600 mb-2">{validationMessage}</p>
					<div class="flex gap-2">
						{#if validationMessage.includes('Song types must total')}
							<button
								type="button"
								onclick={quickFixSongTypes}
								class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
							>
								Fix Song Types
							</button>
						{/if}
						{#if validationMessage.includes('Song selection must total')}
							<button
								type="button"
								onclick={quickFixSongSelection}
								class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
							>
								Fix Song Selection
							</button>
						{/if}
						<button
							type="button"
							onclick={quickFixValues}
							class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
						>
							Fix All
						</button>
					</div>
				</div>
			{/if}
		</div>

		<!-- Song Selection Section -->
		<div class="p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
			<Label class="block mb-3 text-base font-semibold text-gray-800">Song Selection</Label>
			<div class="space-y-4">
				<!-- Random -->
				<div class="space-y-2">
					<Label class="text-sm">Random</Label>
					<div class="px-2">
						{#if editedValue.mode === 'percentage'}
							<RangeSlider
								values={[editedValue.songSelection.random]}
								min={0} max={100} step={1} pips pipstep={25} all="label"
								on:change={(e) => handleSongSelectionSliderChange('random', e)}
								--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
							/>
							<div class="mt-1 flex justify-center">
								<div class="flex items-center space-x-1">
									<Input
										type="number"
										value={editedValue.songSelection.random}
										min={0}
										max={100}
										class="w-16 h-6 text-xs text-center px-1"
										oninput={(e) => handleSongSelectionInputChange('random', e)}
									/>
									<span class="text-xs text-gray-600">%</span>
								</div>
							</div>
						{:else}
							<RangeSlider
								values={[editedValue.songSelection.random]}
								min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
								on:change={(e) => handleSongSelectionSliderChange('random', e)}
								--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
							/>
							<div class="mt-1 flex justify-center">
								<div class="flex items-center space-x-1">
									<Input
										type="number"
										value={editedValue.songSelection.random}
										min={0}
										max={getTotalSongs()}
										class="w-16 h-6 text-xs text-center px-1"
										oninput={(e) => handleSongSelectionInputChange('random', e)}
									/>
									<span class="text-xs text-gray-600">songs</span>
								</div>
							</div>
						{/if}
					</div>
				</div>

				<!-- Watched -->
				<div class="space-y-2">
					<Label class="text-sm">Watched</Label>
					<div class="px-2">
						{#if editedValue.mode === 'percentage'}
							<RangeSlider
								values={[editedValue.songSelection.watched]}
								min={0} max={100} step={1} pips pipstep={25} all="label"
								on:change={(e) => handleSongSelectionSliderChange('watched', e)}
								--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
							/>
							<div class="mt-1 flex justify-center">
								<div class="flex items-center space-x-1">
									<Input
										type="number"
										value={editedValue.songSelection.watched}
										min={0}
										max={100}
										class="w-16 h-6 text-xs text-center px-1"
										oninput={(e) => handleSongSelectionInputChange('watched', e)}
									/>
									<span class="text-xs text-gray-600">%</span>
								</div>
							</div>
						{:else}
							<RangeSlider
								values={[editedValue.songSelection.watched]}
								min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
								on:change={(e) => handleSongSelectionSliderChange('watched', e)}
								--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
							/>
							<div class="mt-1 flex justify-center">
								<div class="flex items-center space-x-1">
									<Input
										type="number"
										value={editedValue.songSelection.watched}
										min={0}
										max={getTotalSongs()}
										class="w-16 h-6 text-xs text-center px-1"
										oninput={(e) => handleSongSelectionInputChange('watched', e)}
									/>
									<span class="text-xs text-gray-600">songs</span>
								</div>
							</div>
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
