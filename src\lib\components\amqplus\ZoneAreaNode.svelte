<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { createEventDispatcher } from 'svelte';

	let { data } = $props();
	const dispatch = createEventDispatcher();

	// Get the color with opacity for background
	const getBackgroundColor = (color) => {
		// Convert hex to rgba with low opacity
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.05)`;
	};

	// Get the color with higher opacity for border
	const getBorderColor = (color) => {
		// Convert hex to rgba with higher opacity
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.8)`;
	};

	const backgroundColor = $derived(getBackgroundColor(data.color));
	const borderColor = $derived(getBorderColor(data.color));

	// Generate summary based on current settings (reactive)
	const summary = $derived(generateSummary(data.currentSettings || data.connectedSettings || {}));



	function generateSummary(settings) {
		if (!settings || Object.keys(settings).length === 0) {
			return 'No settings connected';
		}

		const summaryItems = [];

		// Create a mapping of setting keys to display labels
		const settingLabels = {
			// Mode zone
			scoring: 'Scoring',
			answering: 'Answering',
			// General zone
			players: 'Number of Players',
			'team-size': 'Team Size',
			songs: 'Number of Songs',
			'watched-distribution': 'Watched Distribution',
			'songs-and-types': 'Songs & Types Selection',
			// Quiz zone
			'guess-time': 'Guess Time',
			'extra-time': 'Extra Guess Time',
			'sample-point': 'Sample Point',
			'playback-speed': 'Playback Speed',
			modifiers: 'Modifiers',
			'song-difficulty': 'Song Difficulty',
			// Anime zone
			'anime-type': 'Anime Type',
			'player-score': 'Player Score',
			'anime-score': 'Anime Score',
			vintage: 'Vintage',
			genres: 'Genres',
			tags: 'Tags'
		};

		// Format values appropriately
		function formatValue(key, value) {
			// Handle null/undefined values
			if (value === null || value === undefined) {
				return 'Not set';
			}

			// Handle simple value types first
			if (typeof value !== 'object') {
				switch (key) {
					case 'guess-time':
					case 'extra-time':
						return `${value}s`;
					case 'playback-speed':
						return `${value}`;
					case 'sample-point':
						return `${value}%`;
					default:
						return String(value);
				}
			}

			// Handle array values
			if (Array.isArray(value)) {
				return value.length > 0 ? `${value.length} items` : 'None';
			}

			// Handle complex object types
			switch (key) {
				case 'modifiers':
					const activeModifiers = Object.entries(value)
						.filter(([_, enabled]) => enabled)
						.map(([mod, _]) => mod);
					return activeModifiers.length > 0 ? activeModifiers.join(', ') : 'None';

				case 'anime-type':
					const activeTypes = Object.entries(value)
						.filter(([_, enabled]) => enabled)
						.map(([type, _]) => type.toUpperCase());
					return activeTypes.length > 0 ? activeTypes.join(', ') : 'None';

				case 'song-types-selection':
					if (value.songTypes && value.songSelection) {
						const enabledTypes = Object.keys(value.songTypes).filter(key => value.songTypes[key].enabled);
						return `${enabledTypes.length} song types, ${value.mode} mode`;
					}
					return 'Not configured';



				case 'song-difficulty':
					if (value.easy && value.medium && value.hard) {
						const enabled = [value.easy.enabled, value.medium.enabled, value.hard.enabled].filter(Boolean).length;
						if (value.mode === 'percentage') {
							return `${enabled}/3 difficulties (Easy: ${value.easy.percentage || 0}%, Medium: ${value.medium.percentage || 0}%, Hard: ${value.hard.percentage || 0}%)`;
						}
						return `${enabled}/3 difficulties enabled`;
					}
					return 'Not configured';

				case 'player-score':
				case 'anime-score':
					if (value.mode === 'range') {
						return `${value.min} - ${value.max}`;
					} else if (value.mode === 'percentages') {
						const nonZero = Object.values(value.percentages || {}).filter(p => p > 0).length;
						return `${nonZero} scores with custom percentages`;
					}
					return 'Not configured';

				case 'vintage':
					if (value.ranges && Array.isArray(value.ranges)) {
						if (value.ranges.length === 1) {
							const range = value.ranges[0];
							return `${range.from.season} ${range.from.year} - ${range.to.season} ${range.to.year}`;
						}
						return `${value.ranges.length} vintage ranges`;
					}
					return 'Not configured';

				case 'genres':
				case 'tags':
					if (value.mode) {
						const total = (value.included?.length || 0) + (value.excluded?.length || 0) + (value.optional?.length || 0);
						if (total === 0) {
							return `${value.mode} mode (no filters)`;
						}
						return `${value.mode} mode (${total} items: ${value.included?.length || 0} included, ${value.excluded?.length || 0} excluded, ${value.optional?.length || 0} optional)`;
					}
					return 'Not configured';

				default:
					// Handle generic checkbox objects like { tv: true, movie: false, ... }
					if (typeof value === 'object' && !Array.isArray(value) && value.random === undefined) {
						const enabledKeys = Object.keys(value).filter(key => value[key] === true);
						if (enabledKeys.length > 0) {
							return enabledKeys.length === Object.keys(value).length ? 'All enabled' : `${enabledKeys.join(', ')}`;
						}
						return 'None enabled';
					}

					// Handle number-with-random objects
					if (value.random === true && value.min !== undefined && value.max !== undefined) {
						// Add time unit for time-related settings
						if (key === 'guess-time' || key === 'extra-time') {
							return `Random ${value.min}-${value.max}s`;
						}
						return `Random ${value.min}-${value.max}`;
					}
					if (value.random === false && value.value !== undefined) {
						// Add time unit for time-related settings
						if (key === 'guess-time' || key === 'extra-time') {
							return `${value.value}s`;
						}
						return value.value.toString();
					}

					// Handle select-with-random objects
					if (value.random === true && value.options) {
						const enabledOptions = Object.keys(value.options).filter(key => value.options[key] === true);
						return enabledOptions.length > 0 ? `Random (${enabledOptions.length} options)` : 'Random (none)';
					}
					if (value.random === false && value.value !== undefined && value.options) {
						return `${value.value}x`;
					}

					// Handle range objects
					if (value.min !== undefined && value.max !== undefined) {
						return `${value.min} - ${value.max}`;
					}

					// Handle percentage objects
					if (value.start !== undefined && value.end !== undefined) {
						return `${value.start}% - ${value.end}%`;
					}

					// Fallback for unknown object types
					return JSON.stringify(value);
			}
		}

		// Process all settings dynamically
		Object.entries(settings).forEach(([key, value]) => {
			const label = settingLabels[key] || key;
			const formattedValue = formatValue(key, value);
			summaryItems.push(`${label}: ${formattedValue}`);
		});

		return summaryItems.length > 0 ? summaryItems.join('\n') : 'Default settings';
	}
</script>

<div
	class="relative zone-area-node w-96"
	style="background: {backgroundColor}; border: 3px solid {borderColor}; border-radius: 16px;"
>
	<!-- Input handles for connecting setting nodes -->
	{#if data.zone !== 'mode'}
		<Handle
			type="target"
			position={Position.Left}
			style="width: 16px; height: 16px; background: {data.color}; border: 3px solid white;"
		/>
	{/if}

	<!-- Output handle for flow to next zone -->
	{#if data.zone !== 'anime'}
		<Handle
			type="source"
			position={Position.Right}
			style="width: 16px; height: 16px; background: {data.color}; border: 3px solid white;"
		/>
	{/if}

	<!-- Multiple input handles for individual setting nodes -->
	<Handle
		type="target"
		position={Position.Top}
		id="settings-input"
		style="width: 16px; height: 16px; background: {data.color}; border: 3px solid white; top: -8px;"
	/>

	<Card class="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
		<CardHeader class="pb-4">
			<CardTitle class="flex items-center gap-3 text-xl font-bold text-gray-800">
				<span class="text-2xl">{data.icon}</span>
				<div>
					<div>{data.title}</div>
					<div class="text-sm font-normal text-gray-600">{data.description}</div>
				</div>
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-3">
				<!-- Summary of connected settings -->
				<div class="p-3 rounded-lg bg-gray-50">
					<div class="mb-2 text-sm font-medium text-gray-700">Current Configuration:</div>
					<div class="text-sm leading-relaxed text-gray-600 whitespace-pre-line">
						{summary}
					</div>
				</div>

				<!-- Connection status -->
				<div class="flex items-center gap-2 text-xs text-gray-500">
					<div class="w-2 h-2 bg-green-400 rounded-full"></div>
					<span
						>Zone active • {Object.keys(data.connectedSettings || {}).length} settings connected</span
					>
				</div>
			</div>
		</CardContent>
	</Card>
</div>

<style>
	.zone-area-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	:global(.zone-area-node .svelte-flow__handle) {
		width: 16px;
		height: 16px;
		border: 3px solid white;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	:global(.zone-area-node .svelte-flow__handle.svelte-flow__handle-left) {
		left: -8px;
	}

	:global(.zone-area-node .svelte-flow__handle.svelte-flow__handle-right) {
		right: -8px;
	}

	:global(.zone-area-node .svelte-flow__handle.svelte-flow__handle-top) {
		top: -8px;
	}
</style>
