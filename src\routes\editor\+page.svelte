<script>
	import { SvelteFlow, Controls, Background, MiniMap } from '@xyflow/svelte';
	import '@xyflow/svelte/dist/style.css';
	import SettingsNode from '$lib/components/amqplus/SettingsNode.svelte';
	import ZoneAreaNode from '$lib/components/amqplus/ZoneAreaNode.svelte';
	import IndividualSettingNode from '$lib/components/amqplus/IndividualSettingNode.svelte';
	import ZoneDeleteButton from '$lib/components/amqplus/ZoneDeleteButton.svelte';
	import NodeSidebar from '$lib/components/amqplus/NodeSidebar.svelte';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import ConfirmationModal from '$lib/components/ui/confirmation-modal.svelte';
import { closeAllModals } from '$lib/stores/modalStore.js';

	import '$lib/styles/amqplus.css';

	// Define node types
	const nodeTypes = {
		settings: SettingsNode,
		zoneArea: ZoneAreaNode,
		individualSetting: IndividualSettingNode,
		zoneDeleteButton: ZoneDeleteButton
	};

	// Individual node dimensions (from IndividualSettingNode.svelte - w-48 = 192px)
	const NODE_WIDTH = 192; // Standard width for layout calculations
	const NODE_HEIGHT = 100; // Approximate height of individual setting nodes
	const ZONE_SPACING = 80; // Minimum spacing between zones

	// Centralized zone definitions - shared between default and custom zones
	const zoneDefinitions = {
		mode: {
			title: 'Mode Settings',
			description: 'Scoring & Answering',
			color: '#75B9DF',
			icon: '🎮',
			nodes: [
				{ id: 'scoring', title: 'Scoring', icon: '🏆', defaultValue: 'count' },
				{ id: 'answering', title: 'Answering', icon: '⌨️', defaultValue: 'typing' }
			],
			connectedSettings: {
				scoring: 'count',
				answering: 'typing'
			}
		},
		general: {
			title: 'General Settings',
			description: 'Players & Songs',
			color: '#DF6975',
			icon: '⚙️',
			nodes: [
				{ id: 'players', title: 'Number of Players', icon: '👥', defaultValue: 8 },
				{ id: 'team-size', title: 'Team Size', icon: '👫', defaultValue: 1 },
				{ id: 'watched-distribution', title: 'Watched Distribution', icon: '📊', defaultValue: 'random' },
				{ id: 'songs-and-types', title: 'Songs & Types Selection', icon: '🎵', defaultValue: {
					songCount: { random: false, value: 20, min: 15, max: 25 },
					songTypes: {
						openings: { enabled: true, percentage: 50, count: 10, random: false, percentageMin: 40, percentageMax: 60, countMin: 8, countMax: 12 },
						endings: { enabled: true, percentage: 50, count: 10, random: false, percentageMin: 40, percentageMax: 60, countMin: 8, countMax: 12 },
						inserts: { enabled: false, percentage: 0, count: 0, random: false, percentageMin: 0, percentageMax: 0, countMin: 0, countMax: 0 }
					},
					songSelection: {
						random: { value: 0, randomRange: false, min: 0, max: 10 },
						watched: { value: 100, randomRange: false, min: 90, max: 100 }
					},
					mode: 'percentage'
				}}
			],
			connectedSettings: {
				players: 8,
				'team-size': 1,
				'watched-distribution': 'random',
				'songs-and-types': {
					songCount: { random: false, value: 20, min: 15, max: 25 },
					songTypes: { openings: { enabled: true, percentage: 50, count: 10 }, endings: { enabled: true, percentage: 50, count: 10 }, inserts: { enabled: false, percentage: 0, count: 0 } },
					songSelection: { random: 0, watched: 100 },
					mode: 'percentage'
				}
			}
		},
		quiz: {
			title: 'Quiz Settings',
			description: 'Timing & Difficulty',
			color: '#75DF8B',
			icon: '⏱️',
			nodes: [
				{ id: 'guess-time', title: 'Guess Time', icon: '⏰', defaultValue: { random: false, value: 20, min: 15, max: 25 } },
				{ id: 'extra-time', title: 'Extra Guess Time', icon: '⏱️', defaultValue: { random: false, value: 0, min: 0, max: 5 } },
				{ id: 'sample-point', title: 'Sample Point', icon: '🎯', defaultValue: { useRange: true, start: 1, end: 100, staticValue: 50 } },
				{ id: 'playback-speed', title: 'Playback Speed', icon: '⚡', defaultValue: { random: false, value: 1, options: { 1: true, 1.5: false, 2: false, 4: false } } },
				{ id: 'modifiers', title: 'Modifiers', icon: '🔧', defaultValue: { skipGuessing: true, skipResults: true, queueing: true } },
				{ id: 'song-difficulty', title: 'Song Difficulty', icon: '⭐', defaultValue: {
					easy: { enabled: true, percentage: 33, count: 7 },
					medium: { enabled: true, percentage: 33, count: 7 },
					hard: { enabled: true, percentage: 34, count: 6 },
					mode: 'percentage'
				}}
			],
			connectedSettings: {
				'guess-time': { random: false, value: 20, min: 15, max: 25 },
				'extra-time': { random: false, value: 0, min: 0, max: 5 },
				'sample-point': { useRange: true, start: 1, end: 100, staticValue: 50 },
				'playback-speed': { random: false, value: 1, options: { 1: true, 1.5: false, 2: false, 4: false } },
				modifiers: { skipGuessing: true, skipResults: true, queueing: true },
				'song-difficulty': {
					easy: { enabled: true, percentage: 33, count: 7 },
					medium: { enabled: true, percentage: 33, count: 7 },
					hard: { enabled: true, percentage: 34, count: 6 },
					mode: 'percentage'
				}
			}
		},
		anime: {
			title: 'Anime Settings',
			description: 'Types & Filters',
			color: '#DFB975',
			icon: '📺',
			nodes: [
				{ id: 'anime-type', title: 'Anime Type', icon: '🎬', defaultValue: { tv: true, movie: true, ova: true, ona: true, special: true } },
				{ id: 'player-score', title: 'Player Score', icon: '👤', defaultValue: { min: 1, max: 10, mode: 'range', percentages: {} } },
				{ id: 'anime-score', title: 'Anime Score', icon: '⭐', defaultValue: { min: 2, max: 10, mode: 'range', percentages: {} } },
				{ id: 'vintage', title: 'Vintage', icon: '📅', defaultValue: { ranges: [{ from: { season: 'Winter', year: 1944 }, to: { season: 'Fall', year: 2025 } }] } },
				{ id: 'genres', title: 'Genres', icon: '🎭', defaultValue: { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} } },
				{ id: 'tags', title: 'Tags', icon: '🏷️', defaultValue: { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} } }
			],
			connectedSettings: {
				'anime-type': { tv: true, movie: true, ova: true, ona: true, special: true },
				'player-score': { min: 1, max: 10, mode: 'range', percentages: {} },
				'anime-score': { min: 2, max: 10, mode: 'range', percentages: {} },
				'vintage': { ranges: [{ from: { season: 'Winter', year: 1944 }, to: { season: 'Fall', year: 2025 } }] },
				'genres': { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} },
				'tags': { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} }
			}
		}
	};

	// Default zone configuration (never modified) - used for reset and initial positions
	const defaultZoneConfigs = {
		mode: {
			baseX: 50,
			baseY: 50,
			zoneAreaY: 670, // Leveled with tallest zone (general)
			nodeSpacing: { x: 250, y: 120 }, // Much more spacing to prevent overlap
			nodeStartX: 50, // Padding from left edge of highlight box
			nodeStartY: 40, // Padding from top of highlight box
			columns: 2,
			nodeCount: zoneDefinitions.mode.nodes.length
		},
		general: {
			baseX: 0, // Will be calculated dynamically
			baseY: 50,
			zoneAreaY: 670, // Leveled with other zones
			nodeSpacing: { x: 250, y: 120 }, // Much more spacing to prevent overlap
			nodeStartX: 50, // Padding from left edge of highlight box
			nodeStartY: 40, // Padding from top of highlight box
			columns: 2,
			nodeCount: zoneDefinitions.general.nodes.length
		},
		quiz: {
			baseX: 0, // Will be calculated dynamically
			baseY: 50,
			zoneAreaY: 670, // Leveled with other zones
			nodeSpacing: { x: 250, y: 120 }, // Much more spacing to prevent overlap
			nodeStartX: 50, // Padding from left edge of highlight box
			nodeStartY: 40, // Padding from top of highlight box
			columns: 2,
			nodeCount: zoneDefinitions.quiz.nodes.length
		},
		anime: {
			baseX: 0, // Will be calculated dynamically
			baseY: 50,
			zoneAreaY: 670, // Leveled with other zones
			nodeSpacing: { x: 250, y: 120 }, // Much more spacing to prevent overlap
			nodeStartX: 50, // Padding from left edge of highlight box
			nodeStartY: 40, // Padding from top of highlight box
			columns: 2,
			nodeCount: zoneDefinitions.anime.nodes.length
		}
	};

	// Working zone configuration (gets modified during runtime)
	const zoneConfigs = JSON.parse(JSON.stringify(defaultZoneConfigs));

	// Function to reset zone configs to default values
	const resetZoneConfigsToDefaults = () => {
		Object.keys(zoneConfigs).forEach(zoneName => {
			Object.assign(zoneConfigs[zoneName], defaultZoneConfigs[zoneName]);
		});
	};

	// Function to calculate proper zone positions to prevent overlap
	const calculateZonePositions = () => {
		const zoneOrder = ['mode', 'general', 'quiz', 'anime'];
		let currentX = 50; // Starting X position

		zoneOrder.forEach(zoneName => {
			const config = zoneConfigs[zoneName];
			config.baseX = currentX;

			// Calculate this zone's width and move to next position
			const dimensions = calculateZoneDimensions(config);
			currentX += dimensions.bgWidth + ZONE_SPACING;
		});
	};

	// Function to calculate dynamic zone background dimensions
	const calculateZoneDimensions = (config, actualNodeCount = null) => {
		const nodeCount = actualNodeCount || config.nodeCount;
		const rows = Math.ceil(nodeCount / config.columns);
		const actualColumns = Math.min(nodeCount, config.columns);

		// Calculate width: padding + nodes + spacing between nodes + padding
		const bgWidth = (config.nodeStartX * 2) + // Left and right padding
			(actualColumns * NODE_WIDTH) + // Width of all nodes
			((actualColumns - 1) * (config.nodeSpacing.x - NODE_WIDTH)); // Spacing between nodes

		// Calculate height: padding + nodes + spacing between nodes + padding
		const bgHeight = (config.nodeStartY * 2) + // Top and bottom padding
			(rows * NODE_HEIGHT) + // Height of all nodes
			((rows - 1) * (config.nodeSpacing.y - NODE_HEIGHT)); // Spacing between nodes

		return { bgWidth, bgHeight };
	};

	// Function to calculate dimensions for individual nodes area only (excluding main zone container)
	const calculateIndividualNodesAreaDimensions = (config, actualNodeCount = null) => {
		const nodeCount = actualNodeCount || config.nodeCount;
		const rows = Math.ceil(nodeCount / config.columns);
		const actualColumns = Math.min(nodeCount, config.columns);

		// Calculate width: padding + nodes + spacing between nodes + padding
		const width = (config.nodeStartX * 2) + // Left and right padding
			(actualColumns * NODE_WIDTH) + // Width of all nodes
			((actualColumns - 1) * (config.nodeSpacing.x - NODE_WIDTH)); // Spacing between nodes

		// Calculate height for just the individual nodes area (not including zone container)
		const height = (config.nodeStartY * 2) + // Top and bottom padding
			(rows * NODE_HEIGHT) + // Height of all nodes
			((rows - 1) * (config.nodeSpacing.y - NODE_HEIGHT)); // Spacing between nodes

		return { width, height };
	};

	// Calculate proper zone positions on initialization
	calculateZonePositions();

	// Store original zone positions for drag calculations
	let originalZonePositions = $state.raw({});

	// Initialize original zone positions
	const initializeOriginalZonePositions = () => {
		['mode', 'general', 'quiz', 'anime'].forEach(zone => {
			const config = zoneConfigs[zone];
			const dimensions = calculateZoneDimensions(config);
			originalZonePositions[zone] = {
				baseX: config.baseX,
				baseY: config.baseY,
				zoneAreaX: config.baseX + (dimensions.bgWidth - 384) / 2,
				zoneAreaY: config.baseY + config.zoneAreaY
			};
		});
	};

	// Call initialization
	initializeOriginalZonePositions();

	// Function to update zone background dimensions and positions dynamically
	const updateZoneBackgrounds = () => {
		// First, recalculate zone positions to prevent overlap
		calculateZonePositions();

		// Update original zone positions for drag calculations
		initializeOriginalZonePositions();

		nodes = nodes.map(node => {
			if (node.id.endsWith('-zone-bg')) {
				const zone = node.id.replace('-zone-bg', '');
				const config = zoneConfigs[zone];
				if (config) {
					// Count actual nodes in this zone
					const actualNodeCount = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					).length;

					const dimensions = calculateZoneDimensions(config, actualNodeCount);
					const zoneDefinition = zoneDefinitions[zone];

					// Helper function to convert hex to rgba
					const hexToRgba = (hex, alpha) => {
						const r = parseInt(hex.slice(1, 3), 16);
						const g = parseInt(hex.slice(3, 5), 16);
						const b = parseInt(hex.slice(5, 7), 16);
						return `rgba(${r}, ${g}, ${b}, ${alpha})`;
					};

					return {
						...node,
						position: { x: config.baseX, y: config.baseY }, // Update position
						style: `background: ${hexToRgba(zoneDefinition.color, 0.05)}; border: 2px dashed ${hexToRgba(zoneDefinition.color, 0.3)}; border-radius: 12px; width: ${dimensions.bgWidth}px; height: ${dimensions.bgHeight}px; pointer-events: none;`
					};
				}
			}
			// Also update zone area node positioning to stay centered
			else if (node.id.endsWith('-zone')) {
				const zone = node.id.replace('-zone', '');
				const config = zoneConfigs[zone];
				if (config) {
					// Count actual nodes in this zone
					const actualNodeCount = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					).length;

					const dimensions = calculateZoneDimensions(config, actualNodeCount);

					return {
						...node,
						position: {
							x: config.baseX + (dimensions.bgWidth - 384) / 2, // Center the 384px wide zone area node
							y: config.baseY + config.zoneAreaY
						}
					};
				}
			}
			// Update zone labels
			else if (node.id.endsWith('-zone-label')) {
				const zone = node.id.replace('-zone-label', '');
				const config = zoneConfigs[zone];
				if (config) {
					return {
						...node,
						position: { x: config.baseX + 10, y: config.baseY + 10 }
					};
				}
			}
			// Update zone delete buttons
			else if (node.id.endsWith('-zone-delete-btn')) {
				const zone = node.id.replace('-zone-delete-btn', '').split('-').slice(0, -1).join('-');
				const config = zoneConfigs[zone];
				if (config) {
					// Count actual nodes in this zone
					const actualNodeCount = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					).length;

					const individualNodesAreaDimensions = calculateIndividualNodesAreaDimensions(config, actualNodeCount);

					return {
						...node,
						position: { x: config.baseX, y: config.baseY },
						data: {
							...node.data,
							width: individualNodesAreaDimensions.width,
							height: individualNodesAreaDimensions.height
						}
					};
				}
			}
			// Update individual setting nodes
			else if (node.type === 'individualSetting') {
				const zone = node.data.zone;
				const config = zoneConfigs[zone];
				if (config) {
					// Find this node's index among nodes in the same zone
					const zoneNodes = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					);
					const nodeIndex = zoneNodes.findIndex(n => n.id === node.id);

					if (nodeIndex !== -1) {
						const row = Math.floor(nodeIndex / config.columns);
						const col = nodeIndex % config.columns;

						return {
							...node,
							position: {
								x: config.baseX + config.nodeStartX + (col * config.nodeSpacing.x),
								y: config.baseY + config.nodeStartY + (row * config.nodeSpacing.y)
							}
						};
					}
				}
			}
			return node;
		});
	};

	// Handle individual node deletion
	const handleIndividualNodeDelete = (event) => {
		const { nodeId, zone, timestamp } = event.detail;

		// Show confirmation modal
		showConfirmation(
			'Delete Setting Node',
			`Are you sure you want to delete this setting node?\n\nThis will remove the individual setting node and its connections.\n\nThis action cannot be undone.`,
			() => {
			// Remove the specific individual setting node
			nodes = nodes.filter(node => {
				// Remove the specific individual setting node
				if (node.type === 'individualSetting' &&
					(node.id === nodeId || node.data.title === nodeId) &&
					node.data.parentZoneTimestamp === timestamp) {
					return false;
				}
				return true;
			});

			// Remove related edges
			edges = edges.filter(edge => {
				// Remove edges that connect to nodes we just deleted
				const sourceExists = nodes.some(node => node.id === edge.source);
				const targetExists = nodes.some(node => node.id === edge.target);
				return sourceExists && targetExists;
			});

			// Update zone backgrounds
			updateZoneBackgrounds();
		}
	);
};

	// Function to update a specific zone area node
	const updateZoneAreaNode = (zoneTimestamp) => {
		const zoneAreaNodeIndex = nodes.findIndex(node =>
			node.type === 'zoneArea' && node.data.timestamp === zoneTimestamp
		);

		if (zoneAreaNodeIndex !== -1) {
			const zoneAreaNode = nodes[zoneAreaNodeIndex];
			const connectedSettings = getConnectedSettings(zoneAreaNode.data.zone);

			// Update the zone area node with new connected settings
			nodes[zoneAreaNodeIndex] = {
				...zoneAreaNode,
				data: {
					...zoneAreaNode.data,
					connectedSettings: connectedSettings,
					lastUpdate: Date.now()
				}
			};

			// Trigger reactivity
			nodes = [...nodes];
		}
	};

	// Store reference to the SvelteFlow container for focus restoration
	let svelteFlowContainer = $state(null);
	let svelteFlowKey = $state(0); // Key to force SvelteFlow re-render when needed

	// Function to restore SvelteFlow drag and zoom functionality
	function restoreSvelteFlowFunctionality() {
		if (!svelteFlowContainer) return;

		// Find the SvelteFlow viewport element
		const viewport = svelteFlowContainer.querySelector('.svelte-flow__viewport');
		const pane = svelteFlowContainer.querySelector('.svelte-flow__pane');

		if (viewport) {
			// Restore focus and ensure it's focusable
			viewport.focus();
			if (!viewport.hasAttribute('tabindex')) {
				viewport.setAttribute('tabindex', '-1');
			}

			// Force re-enable pointer events
			viewport.style.pointerEvents = 'auto';

			// Trigger a small transform to refresh event handlers
			const currentTransform = viewport.style.transform;
			viewport.style.transform = currentTransform || 'translate(0px, 0px) scale(1)';
		}

		if (pane) {
			// Ensure pane is interactive
			pane.style.pointerEvents = 'auto';
		}

		// Force a small re-render by triggering a resize event
		setTimeout(() => {
			window.dispatchEvent(new Event('resize'));
		}, 50);
	}

	// Function to handle modal close and restore SvelteFlow functionality
	const handleModalClose = () => {
		console.log('Modal closed, restoring SvelteFlow functionality');
		// Restore focus and re-enable interactions after modal closes
		setTimeout(() => {
			restoreSvelteFlowFunctionality();
			// Force re-render of SvelteFlow component to ensure full restoration
			svelteFlowKey++;
		}, 200);
	};

	// Handle value changes from individual nodes
	const handleNodeValueChange = (changeData) => {
		console.log('Node value changed:', changeData);

		// Find and update the node in the nodes array
		// The nodeId from the dialog might not have the '-setting' suffix, so we need to find by data.id
		const nodeIndex = nodes.findIndex(node =>
			node.type === 'individualSetting' &&
			(node.id === changeData.nodeId ||
			 node.id === `${changeData.nodeId}-setting` ||
			 node.data.id === changeData.nodeId)
		);

		if (nodeIndex !== -1) {
			// Update the node data directly
			nodes[nodeIndex].data.currentValue = changeData.newValue;
			nodes[nodeIndex].data.isDefault = false;
		} else {
			console.warn('Could not find node with ID:', changeData.nodeId);
		}

		// Call the modal close handler to restore functionality
		handleModalClose();
	};

	// Handle zone deletion (delete everything EXCEPT the main zone container)
	const handleZoneDelete = (event) => {
		const { zoneId, timestamp } = event.detail;

		// Show confirmation modal
		showConfirmation(
			`Delete ${zoneId} Zone`,
			`Are you sure you want to delete all individual nodes in this ${zoneId} zone?\n\nThis will remove:\n• The zone background and highlight\n• All individual setting nodes in this zone\n• The zone label\n• All connections to these nodes\n\nThe main zone container will remain.\nThis action cannot be undone.`,
			() => {
			// Close any open modals before deleting nodes to prevent modal/inline editing conflicts
			closeAllModals();

			// Remove all nodes related to this zone instance EXCEPT the main zone container
			const nodesToDelete = [];
			nodes = nodes.filter(node => {
				let shouldDelete = false;
				let reason = '';

				// Remove individual setting nodes with matching parent zone timestamp
				if (node.type === 'individualSetting' && node.data.parentZoneTimestamp === timestamp) {
					shouldDelete = true;
					reason = 'individual setting node with matching parentZoneTimestamp';
				}
				// Remove zone delete button with matching timestamp
				else if (node.type === 'zoneDeleteButton' && node.data.timestamp === timestamp) {
					shouldDelete = true;
					reason = 'zone delete button with matching timestamp';
				}
				// Remove zone background and label with matching timestamp (but NOT zone area)
				else if (node.id.includes(`-${timestamp}`) && !node.id.includes(`${zoneId}-zone-${timestamp}`)) {
					shouldDelete = true;
					reason = 'zone background/label with matching timestamp';
				}
				// For default zones, remove background, label, and delete button but NOT the main zone container
				else if (typeof timestamp === 'string' && timestamp.startsWith('default-')) {
					const baseZoneId = timestamp.replace('default-', '');
					if (node.id === `${baseZoneId}-zone-bg` ||
						node.id === `${baseZoneId}-zone-label` ||
						node.id === `${baseZoneId}-zone-delete-btn`) {
						shouldDelete = true;
						reason = 'default zone background/label/delete-btn';
					}
				}

				if (shouldDelete) {
					nodesToDelete.push({ id: node.id, type: node.type, reason });
					return false;
				}
				return true;
			});



			// Remove related edges
			edges = edges.filter(edge => {
				// Remove edges that connect to nodes we just deleted
				const sourceExists = nodes.some(node => node.id === edge.source);
				const targetExists = nodes.some(node => node.id === edge.target);
				return sourceExists && targetExists;
			});

			// Update zone backgrounds
			updateZoneBackgrounds();

			// Update zone summaries to reflect the changes
			updateZoneSummaries();
		}
	);
};

	// Function to create initial nodes with current zone configs
	const createInitialNodes = () => [
		// Zone Background Areas (Visual boundaries) - Tower layout with dynamic dimensions
		{
			id: 'mode-zone-bg',
			type: 'default',
			position: { x: zoneConfigs.mode.baseX, y: zoneConfigs.mode.baseY },
			data: { label: '' },
			style: `background: rgba(117, 185, 223, 0.05); border: 2px dashed rgba(117, 185, 223, 0.3); border-radius: 12px; width: ${calculateZoneDimensions(zoneConfigs.mode).bgWidth}px; height: ${calculateZoneDimensions(zoneConfigs.mode).bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'general-zone-bg',
			type: 'default',
			position: { x: zoneConfigs.general.baseX, y: zoneConfigs.general.baseY },
			data: { label: '' },
			style: `background: rgba(223, 105, 117, 0.05); border: 2px dashed rgba(223, 105, 117, 0.3); border-radius: 12px; width: ${calculateZoneDimensions(zoneConfigs.general).bgWidth}px; height: ${calculateZoneDimensions(zoneConfigs.general).bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'quiz-zone-bg',
			type: 'default',
			position: { x: zoneConfigs.quiz.baseX, y: zoneConfigs.quiz.baseY },
			data: { label: '' },
			style: `background: rgba(117, 223, 139, 0.05); border: 2px dashed rgba(117, 223, 139, 0.3); border-radius: 12px; width: ${calculateZoneDimensions(zoneConfigs.quiz).bgWidth}px; height: ${calculateZoneDimensions(zoneConfigs.quiz).bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'anime-zone-bg',
			type: 'default',
			position: { x: zoneConfigs.anime.baseX, y: zoneConfigs.anime.baseY },
			data: { label: '' },
			style: `background: rgba(223, 185, 117, 0.05); border: 2px dashed rgba(223, 185, 117, 0.3); border-radius: 12px; width: ${calculateZoneDimensions(zoneConfigs.anime).bgWidth}px; height: ${calculateZoneDimensions(zoneConfigs.anime).bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		},

		// Zone Labels
		{
			id: 'mode-zone-label',
			type: 'default',
			position: { x: zoneConfigs.mode.baseX + 10, y: zoneConfigs.mode.baseY + 10 },
			data: { label: '🎮 Mode Zone' },
			style: 'background: transparent; border: none; color: rgba(117, 185, 223, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;',
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'general-zone-label',
			type: 'default',
			position: { x: zoneConfigs.general.baseX + 10, y: zoneConfigs.general.baseY + 10 },
			data: { label: '⚙️ General Zone' },
			style: 'background: transparent; border: none; color: rgba(223, 105, 117, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;',
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'quiz-zone-label',
			type: 'default',
			position: { x: zoneConfigs.quiz.baseX + 10, y: zoneConfigs.quiz.baseY + 10 },
			data: { label: '⏱️ Quiz Zone' },
			style: 'background: transparent; border: none; color: rgba(117, 223, 139, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;',
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'anime-zone-label',
			type: 'default',
			position: { x: zoneConfigs.anime.baseX + 10, y: zoneConfigs.anime.baseY + 10 },
			data: { label: '📺 Anime Zone' },
			style: 'background: transparent; border: none; color: rgba(223, 185, 117, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;',
			selectable: false,
			draggable: false,
			deletable: false
		},

		// Zone Area Nodes (Main areas that show summaries) - Positioned at bottom of zones, centered
		{
			id: 'mode-zone',
			type: 'zoneArea',
			position: {
				x: zoneConfigs.mode.baseX + (calculateZoneDimensions(zoneConfigs.mode).bgWidth - 384) / 2, // Center the 384px wide zone area node
				y: zoneConfigs.mode.baseY + zoneConfigs.mode.zoneAreaY
			},
			data: {
				title: zoneDefinitions.mode.title,
				description: zoneDefinitions.mode.description,
				icon: zoneDefinitions.mode.icon,
				color: zoneDefinitions.mode.color,
				zone: 'mode',
				connectedSettings: zoneDefinitions.mode.connectedSettings,
				timestamp: 'default-mode'
			},
			deletable: false,
			draggable: true
		},
		{
			id: 'general-zone',
			type: 'zoneArea',
			position: {
				x: zoneConfigs.general.baseX + (calculateZoneDimensions(zoneConfigs.general).bgWidth - 384) / 2, // Center the 384px wide zone area node
				y: zoneConfigs.general.baseY + zoneConfigs.general.zoneAreaY
			},
			data: {
				title: zoneDefinitions.general.title,
				description: zoneDefinitions.general.description,
				icon: zoneDefinitions.general.icon,
				color: zoneDefinitions.general.color,
				zone: 'general',
				connectedSettings: zoneDefinitions.general.connectedSettings,
				timestamp: 'default-general'
			},
			deletable: false,
			draggable: true
		},
		{
			id: 'quiz-zone',
			type: 'zoneArea',
			position: {
				x: zoneConfigs.quiz.baseX + (calculateZoneDimensions(zoneConfigs.quiz).bgWidth - 384) / 2, // Center the 384px wide zone area node
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.zoneAreaY
			},
			data: {
				title: zoneDefinitions.quiz.title,
				description: zoneDefinitions.quiz.description,
				icon: zoneDefinitions.quiz.icon,
				color: zoneDefinitions.quiz.color,
				zone: 'quiz',
				connectedSettings: zoneDefinitions.quiz.connectedSettings,
				timestamp: 'default-quiz'
			},
			deletable: false,
			draggable: true
		},
		{
			id: 'anime-zone',
			type: 'zoneArea',
			position: {
				x: zoneConfigs.anime.baseX + (calculateZoneDimensions(zoneConfigs.anime).bgWidth - 384) / 2, // Center the 384px wide zone area node
				y: zoneConfigs.anime.baseY + zoneConfigs.anime.zoneAreaY
			},
			data: {
				title: zoneDefinitions.anime.title,
				description: zoneDefinitions.anime.description,
				icon: zoneDefinitions.anime.icon,
				color: zoneDefinitions.anime.color,
				zone: 'anime',
				connectedSettings: zoneDefinitions.anime.connectedSettings,
				timestamp: 'default-anime'
			},
			deletable: false,
			draggable: true
		},

		// Zone Delete Buttons - positioned over individual nodes areas
		{
			id: 'mode-zone-delete-btn',
			type: 'zoneDeleteButton',
			position: {
				x: zoneConfigs.mode.baseX,
				y: zoneConfigs.mode.baseY
			},
			data: {
				zone: 'mode',
				timestamp: 'default-mode',
				width: calculateIndividualNodesAreaDimensions(zoneConfigs.mode).width,
				height: calculateIndividualNodesAreaDimensions(zoneConfigs.mode).height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'general-zone-delete-btn',
			type: 'zoneDeleteButton',
			position: {
				x: zoneConfigs.general.baseX,
				y: zoneConfigs.general.baseY
			},
			data: {
				zone: 'general',
				timestamp: 'default-general',
				width: calculateIndividualNodesAreaDimensions(zoneConfigs.general).width,
				height: calculateIndividualNodesAreaDimensions(zoneConfigs.general).height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'quiz-zone-delete-btn',
			type: 'zoneDeleteButton',
			position: {
				x: zoneConfigs.quiz.baseX,
				y: zoneConfigs.quiz.baseY
			},
			data: {
				zone: 'quiz',
				timestamp: 'default-quiz',
				width: calculateIndividualNodesAreaDimensions(zoneConfigs.quiz).width,
				height: calculateIndividualNodesAreaDimensions(zoneConfigs.quiz).height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'anime-zone-delete-btn',
			type: 'zoneDeleteButton',
			position: {
				x: zoneConfigs.anime.baseX,
				y: zoneConfigs.anime.baseY
			},
			data: {
				zone: 'anime',
				timestamp: 'default-anime',
				width: calculateIndividualNodesAreaDimensions(zoneConfigs.anime).width,
				height: calculateIndividualNodesAreaDimensions(zoneConfigs.anime).height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		},

		// Individual Setting Nodes - Mode Zone - 2 columns tower layout
		...zoneDefinitions.mode.nodes.map((nodeData, index) => {
			const row = Math.floor(index / zoneConfigs.mode.columns);
			const col = index % zoneConfigs.mode.columns;

			return {
				id: `${nodeData.id}-setting`,
				type: 'individualSetting',
				position: {
					x: zoneConfigs.mode.baseX + zoneConfigs.mode.nodeStartX + (col * zoneConfigs.mode.nodeSpacing.x),
					y: zoneConfigs.mode.baseY + zoneConfigs.mode.nodeStartY + (row * zoneConfigs.mode.nodeSpacing.y)
				},
				data: {
					id: nodeData.id,
					title: nodeData.title,
					icon: nodeData.icon,
					color: zoneDefinitions.mode.color,
					zone: 'mode',
					currentValue: nodeData.defaultValue,
					isDefault: true,
					parentZoneTimestamp: 'default-mode',
					onDelete: handleIndividualNodeDelete,
					onValueChange: handleNodeValueChange,
					onModalClose: handleModalClose
				},
				deletable: false,
				draggable: false
			};
		}),

		// Individual Setting Nodes - General Zone - 2 columns tower layout
		...zoneDefinitions.general.nodes.map((nodeData, index) => {
			const row = Math.floor(index / zoneConfigs.general.columns);
			const col = index % zoneConfigs.general.columns;

			return {
				id: `${nodeData.id}-setting`,
				type: 'individualSetting',
				position: {
					x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX + (col * zoneConfigs.general.nodeSpacing.x),
					y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY + (row * zoneConfigs.general.nodeSpacing.y)
				},
				data: {
					id: nodeData.id,
					title: nodeData.title,
					icon: nodeData.icon,
					color: zoneDefinitions.general.color,
					zone: 'general',
					currentValue: nodeData.defaultValue,
					isDefault: true,
					parentZoneTimestamp: 'default-general',
					onDelete: handleIndividualNodeDelete,
					onValueChange: handleNodeValueChange,
					onModalClose: handleModalClose
				},
				deletable: false,
				draggable: false
			};
		}),

		// Individual Setting Nodes - Quiz Zone - 2 columns tower layout
		...zoneDefinitions.quiz.nodes.map((nodeData, index) => {
			const row = Math.floor(index / zoneConfigs.quiz.columns);
			const col = index % zoneConfigs.quiz.columns;

			return {
				id: `${nodeData.id}-setting`,
				type: 'individualSetting',
				position: {
					x: zoneConfigs.quiz.baseX + zoneConfigs.quiz.nodeStartX + (col * zoneConfigs.quiz.nodeSpacing.x),
					y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.nodeStartY + (row * zoneConfigs.quiz.nodeSpacing.y)
				},
				data: {
					id: nodeData.id,
					title: nodeData.title,
					icon: nodeData.icon,
					color: zoneDefinitions.quiz.color,
					zone: 'quiz',
					currentValue: nodeData.defaultValue,
					isDefault: true,
					parentZoneTimestamp: 'default-quiz',
					onDelete: handleIndividualNodeDelete,
					onValueChange: handleNodeValueChange,
					onModalClose: handleModalClose
				},
				deletable: false,
				draggable: false
			};
		}),

		// Individual Setting Nodes - Anime Zone - 2 columns tower layout
		...zoneDefinitions.anime.nodes.map((nodeData, index) => {
			const row = Math.floor(index / zoneConfigs.anime.columns);
			const col = index % zoneConfigs.anime.columns;

			return {
				id: `${nodeData.id}-setting`,
				type: 'individualSetting',
				position: {
					x: zoneConfigs.anime.baseX + zoneConfigs.anime.nodeStartX + (col * zoneConfigs.anime.nodeSpacing.x),
					y: zoneConfigs.anime.baseY + zoneConfigs.anime.nodeStartY + (row * zoneConfigs.anime.nodeSpacing.y)
				},
				data: {
					id: nodeData.id,
					title: nodeData.title,
					icon: nodeData.icon,
					color: zoneDefinitions.anime.color,
					zone: 'anime',
					currentValue: nodeData.defaultValue,
					isDefault: true,
					parentZoneTimestamp: 'default-anime',
					onDelete: handleIndividualNodeDelete,
					onValueChange: handleNodeValueChange,
					onModalClose: handleModalClose
				},
				deletable: false,
				draggable: false
			};
		})
	];

	// Create initial nodes with default positions
	let initialNodes = createInitialNodes();

	// Define initial edges (connections between nodes)
	let initialEdges = [
		// Zone flow connections
		{
			id: 'e-mode-general',
			source: 'mode-zone',
			target: 'general-zone',
			type: 'step',
			style: 'stroke: #75B9DF; stroke-width: 3; stroke-dasharray: 8,4;',
			animated: true
		},
		{
			id: 'e-general-quiz',
			source: 'general-zone',
			target: 'quiz-zone',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 3; stroke-dasharray: 8,4;',
			animated: true
		},
		{
			id: 'e-quiz-anime',
			source: 'quiz-zone',
			target: 'anime-zone',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 3; stroke-dasharray: 8,4;',
			animated: true
		},

		// Individual setting connections to zones - Mode
		{
			id: 'e-scoring-mode',
			source: 'scoring-setting',
			target: 'mode-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75B9DF; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-answering-mode',
			source: 'answering-setting',
			target: 'mode-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75B9DF; stroke-width: 2;',
			animated: false
		},

		// Individual setting connections to zones - General
		{
			id: 'e-players-general',
			source: 'players-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-team-size-general',
			source: 'team-size-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-songs-general',
			source: 'songs-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-song-selection-general',
			source: 'song-selection-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-watched-distribution-general',
			source: 'watched-distribution-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-song-types-general',
			source: 'song-types-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-openings-categories-general',
			source: 'openings-categories-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-endings-categories-general',
			source: 'endings-categories-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-inserts-categories-general',
			source: 'inserts-categories-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},

		// Individual setting connections to zones - Quiz
		{
			id: 'e-guess-time-quiz',
			source: 'guess-time-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-extra-time-quiz',
			source: 'extra-time-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-sample-point-quiz',
			source: 'sample-point-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-playback-speed-quiz',
			source: 'playback-speed-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-song-difficulty-quiz',
			source: 'song-difficulty-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-song-popularity-quiz',
			source: 'song-popularity-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-modifiers-quiz',
			source: 'modifiers-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},

		// Individual setting connections to zones - Anime
		{
			id: 'e-player-score-anime',
			source: 'player-score-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-anime-score-anime',
			source: 'anime-score-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-vintage-anime',
			source: 'vintage-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-anime-type-anime',
			source: 'anime-type-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-genres-anime',
			source: 'genres-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-tags-anime',
			source: 'tags-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		}
	];

	// Reactive variables for the flow
	let nodes = $state(initialNodes);
	let edges = $state(initialEdges);

	// Update zone backgrounds after initialization to ensure proper positioning
	updateZoneBackgrounds();

	// Update zone summaries after initialization to ensure reactive data is available
	updateZoneSummaries();

	// Reactive effect to update zone summaries when individual nodes change
	$effect(() => {
		console.log('Zone summary reactive effect triggered, processing', nodes.length, 'nodes');

		// Update zone area nodes with current individual node data
		nodes.forEach((node, index) => {
			if (node.type === 'zoneArea') {
				// Find all individual setting nodes in this zone
				const settingNodes = nodes.filter(n =>
					n.type === 'individualSetting' &&
					n.data.zone === node.data.zone &&
					n.data.parentZoneTimestamp === node.data.timestamp
				);

				// Build current settings object
				const currentSettings = {};
				settingNodes.forEach(settingNode => {
					if (settingNode.data.id && settingNode.data.currentValue !== undefined) {
						currentSettings[settingNode.data.id] = settingNode.data.currentValue;
					}
				});

				// Only update if settings have actually changed
				const currentSettingsStr = JSON.stringify(currentSettings);
				const existingSettingsStr = JSON.stringify(node.data.currentSettings || {});

				if (currentSettingsStr !== existingSettingsStr) {
					console.log(`Zone ${node.data.zone} settings changed:`, currentSettings);

					// Update the node data directly
					node.data.currentSettings = currentSettings;
					node.data.lastUpdate = Date.now();
				}
			}
		});
	});

	// UI state
	let sidePanelOpen = $state(false);
	let nodeSidebarOpen = $state(false);

	// Confirmation modal state
	let confirmationModalOpen = $state(false);
	let confirmationModalData = $state({
		title: '',
		description: '',
		onConfirm: () => {},
		onCancel: () => {}
	});

	// Update zone summaries when individual settings change (removed to prevent infinite loop)
	// This will be handled manually when settings actually change

	// Helper function to show confirmation modal
	const showConfirmation = (title, description, onConfirm, onCancel = () => {}) => {
		confirmationModalData = {
			title,
			description,
			onConfirm,
			onCancel
		};
		confirmationModalOpen = true;
	};

	// Helper functions for future manual updates
	function getConnectedSettings(zone) {
		const settingNodes = nodes.filter(node =>
			node.type === 'individualSetting' && node.data.zone === zone
		);

		const settings = {};
		settingNodes.forEach(node => {
			// Use the node's ID directly instead of transforming the title
			// This ensures consistency with the field configurations
			const settingKey = node.data.id;
			settings[settingKey] = node.data.currentValue;
		});

		return settings;
	}

	// Manual update function (call when needed)
	function updateZoneSummaries() {
		// The reactive $effect will handle this automatically now
		// This function is kept for compatibility but does nothing
		// since the reactive effect handles zone summary updates
	}




	// Event handlers
	const onNodeDrag = (event) => {
		// SvelteFlow passes the node in event.targetNode
		if (!event || !event.targetNode) {
			console.warn('onNodeDrag: Could not find targetNode in event', event);
			return;
		}

		const node = event.targetNode;

		// If a zone area node is being dragged, move all other nodes in that zone (background, label, individual nodes)
		// The zone area node itself moves naturally with the drag
		if (node.type === 'zoneArea') {
			const zone = node.data.zone;
			const config = zoneConfigs[zone];
			const originalPos = originalZonePositions[zone];

			if (originalPos) {
				// Calculate the offset from the zone area node's original position
				const deltaX = node.position.x - originalPos.zoneAreaX;
				const deltaY = node.position.y - originalPos.zoneAreaY;

				// Update the zone's base position
				const newBaseX = originalPos.baseX + deltaX;
				const newBaseY = originalPos.baseY + deltaY;

				// Update all nodes in this zone, including the zone area node to ensure consistency
				nodes = nodes.map(n => {
					// Update the zone area node position to match the dragged position
					if (n.id === node.id) {
						return {
							...n,
							position: {
								x: node.position.x,
								y: node.position.y
							}
						};
					}

					// Move zone background (both default and custom with timestamps)
					if (n.id === `${zone}-zone-bg` || n.id.startsWith(`${zone}-zone-bg-`)) {
						return {
							...n,
							position: {
								x: newBaseX,
								y: newBaseY
							}
						};
					}
					// Move zone label (both default and custom with timestamps)
					else if (n.id === `${zone}-zone-label` || n.id.startsWith(`${zone}-zone-label-`)) {
						return {
							...n,
							position: {
								x: newBaseX + 10,
								y: newBaseY + 10
							}
						};
					}
					// Move individual setting nodes
					else if (n.type === 'individualSetting' && n.data.zone === zone) {
						// Find this node's index among nodes in the same zone
						const zoneNodes = nodes.filter(zoneNode =>
							zoneNode.type === 'individualSetting' && zoneNode.data.zone === zone
						);
						const nodeIndex = zoneNodes.findIndex(zn => zn.id === n.id);

						if (nodeIndex !== -1) {
							const row = Math.floor(nodeIndex / config.columns);
							const col = nodeIndex % config.columns;

							return {
								...n,
								position: {
									x: newBaseX + config.nodeStartX + (col * config.nodeSpacing.x),
									y: newBaseY + config.nodeStartY + (row * config.nodeSpacing.y)
								}
							};
						}
					}
					// Move zone delete button
					else if (n.id.includes(`${zone}-zone-delete-btn`)) {
						return {
							...n,
							position: {
								x: newBaseX,
								y: newBaseY
							}
						};
					}
					return n;
				});
			}
		}
	};

	const onNodeDragStop = (event) => {
		// SvelteFlow passes the node in event.targetNode
		if (!event || !event.targetNode) {
			console.warn('onNodeDragStop: Could not find targetNode in event', event);
			return;
		}

		const node = event.targetNode;

		// If a zone area node was dragged, update the zone configuration with new positions
		if (node.type === 'zoneArea') {
			const zone = node.data.zone;
			const config = zoneConfigs[zone];
			const originalPos = originalZonePositions[zone];

			if (originalPos) {
				// Calculate the offset from the zone area node's original position
				const deltaX = node.position.x - originalPos.zoneAreaX;
				const deltaY = node.position.y - originalPos.zoneAreaY;

				// Update the zone's base position in the configuration
				config.baseX = originalPos.baseX + deltaX;
				config.baseY = originalPos.baseY + deltaY;

				// Update the original positions for future drags
				const dimensions = calculateZoneDimensions(config);
				originalZonePositions[zone] = {
					baseX: config.baseX,
					baseY: config.baseY,
					zoneAreaX: config.baseX + (dimensions.bgWidth - 384) / 2,
					zoneAreaY: config.baseY + config.zoneAreaY
				};
			}
		}


	};

	const onNodeClick = (event) => {
		// Handle node click events if needed
	};

	const onEdgeClick = (event) => {
		// Handle edge click events if needed
	};

	// Prevent deletion of zone areas and individual setting nodes
	const onNodesDelete = (event) => {
		// Prevent deletion by returning false
		event.preventDefault?.();
		return false;
	};

	// Prevent disconnection of edges from zone areas
	const onEdgesDelete = (event) => {
		// Prevent deletion by returning false
		event.preventDefault?.();
		return false;
	};

	// Toggle side panel
	const toggleSidePanel = () => {
		sidePanelOpen = !sidePanelOpen;
	};

	// Get list of zones that already have individual nodes
	const getExistingZones = () => {
		const existingZones = [];
		const zoneKeys = ['mode', 'general', 'quiz', 'anime'];

		zoneKeys.forEach(zoneKey => {
			const hasIndividualNodes = nodes.some(node =>
				node.type === 'individualSetting' && node.data.zone === zoneKey
			);
			if (hasIndividualNodes) {
				existingZones.push(zoneKey);
			}
		});

		return existingZones;
	};

	// Export configuration
	const exportConfig = () => {
		const config = {
			nodes: nodes.map(node => ({
				id: node.id,
				zone: node.data.zone,
				settings: node.data.settings
			})),
			timestamp: new Date().toISOString()
		};
		
		const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = 'amq-lobby-config.json';
		a.click();
		URL.revokeObjectURL(url);
	};

	// Reset to defaults with confirmation
	const resetToDefaults = () => {
		showConfirmation(
			'Reset to Defaults',
			"Are you sure you want to restore everything to defaults?\n\nThis will:\n• Reset all node positions\n• Remove any custom nodes you've added\n• Reset all settings to their initial values\n• Clear any custom configurations\n\nThis action cannot be undone.",
			() => {
			// First reset zone configs to default values
			resetZoneConfigsToDefaults();

			// Then recalculate zone positions
			calculateZonePositions();

			// Recreate initial nodes with default positions
			initialNodes = createInitialNodes();

			// Reset nodes and edges to initial state
			nodes = [...initialNodes];
			edges = [...initialEdges];

			// Update zone backgrounds and original positions
			updateZoneBackgrounds();
		}
	);
};

	// Add individual nodes and zone background from sidebar
	const handleAddNode = (zoneKey, zoneData) => {
		// Check if individual nodes already exist for this zone
		const existingIndividualNodes = nodes.find(node =>
			node.type === 'individualSetting' && node.data.zone === zoneKey
		);

		if (existingIndividualNodes) {
			alert(`Individual nodes for the ${zoneKey} zone already exist. You cannot add duplicate individual nodes.`);
			return;
		}

		// Use centralized zone definition instead of sidebar data
		const zoneDefinition = zoneDefinitions[zoneKey];
		if (!zoneDefinition) {
			alert(`Zone definition for ${zoneKey} not found.`);
			return;
		}

	// Close any open modals before creating new nodes to prevent modal/inline editing conflicts
	closeAllModals();

		const timestamp = Date.now();
		const newNodes = [];
		const newEdges = [];

		// Find the existing main zone container to position individual nodes around it
		const existingZoneContainer = nodes.find(n => n.type === 'zoneArea' && n.data.zone === zoneKey);

		if (!existingZoneContainer) {
			alert(`Main zone container for ${zoneKey} not found. Cannot add individual nodes.`);
			return;
		}

		// Use the zone configuration to get the base position for individual nodes
		const config = zoneConfigs[zoneKey];
		const newZoneBaseX = config.baseX;
		const newZoneBaseY = config.baseY;

		// Create zone background
		const zoneBgId = `${zoneKey}-zone-bg-${timestamp}`;
		const zoneDimensions = calculateZoneDimensions({
			...zoneConfigs[zoneKey],
			baseX: newZoneBaseX,
			baseY: newZoneBaseY,
			nodeCount: zoneDefinition.nodes.length
		});

		// Helper function to convert hex to rgba
		const hexToRgba = (hex, alpha) => {
			const r = parseInt(hex.slice(1, 3), 16);
			const g = parseInt(hex.slice(3, 5), 16);
			const b = parseInt(hex.slice(5, 7), 16);
			return `rgba(${r}, ${g}, ${b}, ${alpha})`;
		};

		newNodes.push({
			id: zoneBgId,
			type: 'default',
			position: { x: newZoneBaseX, y: newZoneBaseY },
			data: { label: '' },
			style: `background: ${hexToRgba(zoneDefinition.color, 0.05)}; border: 2px dashed ${hexToRgba(zoneDefinition.color, 0.3)}; border-radius: 12px; width: ${zoneDimensions.bgWidth}px; height: ${zoneDimensions.bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		});

		// Create zone label
		const zoneLabelId = `${zoneKey}-zone-label-${timestamp}`;
		newNodes.push({
			id: zoneLabelId,
			type: 'default',
			position: { x: newZoneBaseX + 10, y: newZoneBaseY + 10 },
			data: { label: `${zoneDefinition.icon} ${zoneDefinition.title}` },
			style: `background: transparent; border: none; color: ${hexToRgba(zoneDefinition.color, 0.8)}; font-weight: bold; font-size: 14px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		});

		// Create zone delete button for individual nodes area
		const zoneDeleteBtnId = `${zoneKey}-zone-delete-btn-${timestamp}`;
		const individualNodesAreaDimensions = calculateIndividualNodesAreaDimensions({
			...zoneConfigs[zoneKey],
			nodeCount: zoneDefinition.nodes.length
		});

		newNodes.push({
			id: zoneDeleteBtnId,
			type: 'zoneDeleteButton',
			position: {
				x: newZoneBaseX,
				y: newZoneBaseY
			},
			data: {
				zone: zoneKey,
				timestamp: timestamp,
				width: individualNodesAreaDimensions.width,
				height: individualNodesAreaDimensions.height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		});



		// Create individual setting nodes
		zoneDefinition.nodes.forEach((nodeData, index) => {
			const row = Math.floor(index / zoneConfigs[zoneKey].columns);
			const col = index % zoneConfigs[zoneKey].columns;

			const nodeId = `${nodeData.id}-${timestamp}`;
			const nodePosition = {
				x: newZoneBaseX + zoneConfigs[zoneKey].nodeStartX + (col * zoneConfigs[zoneKey].nodeSpacing.x),
				y: newZoneBaseY + zoneConfigs[zoneKey].nodeStartY + (row * zoneConfigs[zoneKey].nodeSpacing.y)
			};

			newNodes.push({
				id: nodeId,
				type: 'individualSetting',
				position: nodePosition,
				data: {
					id: nodeData.id, // Ensure the id is set correctly for field type detection
					title: nodeData.title,
					icon: nodeData.icon,
					color: zoneDefinition.color,
					zone: zoneKey,
					currentValue: nodeData.defaultValue,
					isDefault: false,
					parentZoneTimestamp: timestamp,
					onDelete: handleIndividualNodeDelete,
					onValueChange: handleNodeValueChange,
					onModalClose: handleModalClose
				},
				deletable: true,
				draggable: false
			});

			// Create edge connecting individual node to zone area
			newEdges.push({
				id: `e-${nodeId}-${existingZoneContainer.id}`,
				source: nodeId,
				target: existingZoneContainer.id,
				targetHandle: 'settings-input',
				type: 'step',
				style: `stroke: ${zoneDefinition.color}; stroke-width: 2;`,
				animated: false
			});
		});

		// Add all new nodes and edges
		nodes = [...nodes, ...newNodes];
		edges = [...edges, ...newEdges];

		// Update zone backgrounds to ensure proper positioning
		updateZoneBackgrounds();

		// Update zone summaries to reflect the new individual nodes
		updateZoneSummaries();

		// Close sidebar after adding
		nodeSidebarOpen = false;
	};



	// Apply automatic layout (reset positions only, keep custom nodes and settings)
	const applyAutoLayout = () => {
		// First reset zone configs to default values
		resetZoneConfigsToDefaults();

		// Then recalculate zone positions to get proper defaults
		calculateZonePositions();

		// Reset original zone positions to match the recalculated positions
		initializeOriginalZonePositions();

		// Update zone backgrounds to ensure proper positioning
		updateZoneBackgrounds();

		// Reset all node positions to their default calculated positions
		// This keeps any custom nodes that were added but resets their positions
		nodes = nodes.map(node => {
			// For zone backgrounds, use current zone config
			if (node.id.endsWith('-zone-bg')) {
				const zone = node.id.replace('-zone-bg', '');
				const config = zoneConfigs[zone];
				return {
					...node,
					position: { x: config.baseX, y: config.baseY }
				};
			}
			// For zone labels
			else if (node.id.endsWith('-zone-label')) {
				const zone = node.id.replace('-zone-label', '');
				const config = zoneConfigs[zone];
				return {
					...node,
					position: { x: config.baseX + 10, y: config.baseY + 10 }
				};
			}
			// For zone area nodes
			else if (node.type === 'zoneArea') {
				const zone = node.data.zone;
				const config = zoneConfigs[zone];
				const dimensions = calculateZoneDimensions(config);
				return {
					...node,
					position: {
						x: config.baseX + (dimensions.bgWidth - 384) / 2,
						y: config.baseY + config.zoneAreaY
					}
				};
			}
			// For zone delete buttons
			else if (node.id.endsWith('-zone-delete-btn')) {
				const zone = node.id.replace('-zone-delete-btn', '').split('-').slice(0, -1).join('-');
				const config = zoneConfigs[zone];
				if (config) {
					const actualNodeCount = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					).length;
					const individualNodesAreaDimensions = calculateIndividualNodesAreaDimensions(config, actualNodeCount);

					return {
						...node,
						position: { x: config.baseX, y: config.baseY },
						data: {
							...node.data,
							width: individualNodesAreaDimensions.width,
							height: individualNodesAreaDimensions.height
						}
					};
				}
			}
			// For individual setting nodes (including custom ones)
			else if (node.type === 'individualSetting') {
				const zone = node.data.zone;
				const config = zoneConfigs[zone];
				const zoneNodes = nodes.filter(n =>
					n.type === 'individualSetting' && n.data.zone === zone
				);
				const nodeIndex = zoneNodes.findIndex(n => n.id === node.id);

				if (nodeIndex !== -1) {
					const row = Math.floor(nodeIndex / config.columns);
					const col = nodeIndex % config.columns;

					return {
						...node,
						position: {
							x: config.baseX + config.nodeStartX + (col * config.nodeSpacing.x),
							y: config.baseY + config.nodeStartY + (row * config.nodeSpacing.y)
						}
					};
				}
			}

			return node;
		});
	};
</script>

<svelte:head>
	<title>AMQ PLUS - Node Editor</title>
	<meta name="description" content="Configure your AMQ lobby settings with our visual node-based editor" />
</svelte:head>

<!-- Fullscreen Node Editor -->
<div class="relative w-full h-full">
	<!-- Side Panel Toggle Button -->
	<button
		onclick={toggleSidePanel}
		class="fixed z-50 p-2 transition-colors border rounded-lg shadow-lg top-4 left-4 bg-white/95 backdrop-blur-sm border-amq-light hover:bg-white"
		title={sidePanelOpen ? 'Close panel' : 'Open panel'}
		aria-label={sidePanelOpen ? 'Close panel' : 'Open panel'}
	>
		<svg
			class="w-5 h-5 text-gray-600 transition-transform duration-200 {sidePanelOpen ? 'rotate-180' : ''}"
			fill="none"
			stroke="currentColor"
			viewBox="0 0 24 24"
		>
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
		</svg>
	</button>

	<!-- Side Panel -->
	<div class="fixed top-0 left-0 h-full z-40 transition-transform duration-300 ease-in-out {sidePanelOpen ? 'translate-x-0' : '-translate-x-full'}">
		<div class="flex flex-col h-full border-r shadow-xl w-80 bg-white/95 backdrop-blur-sm border-amq-light">
			<!-- Panel Header with top margin to avoid arrow -->
			<div class="p-6 pt-12 border-b border-amq-light">
				<div class="flex items-center mb-4 space-x-3">
					<a href="/" class="text-2xl font-bold amq-gradient-text">AMQ PLUS</a>
					<span class="text-gray-400">|</span>
					<h1 class="text-lg font-semibold text-gray-800">Node Editor</h1>
				</div>
				<p class="text-sm text-gray-600">
					Configure your AMQ lobby settings with our visual node-based editor.
				</p>
			</div>

			<!-- Panel Content -->
			<div class="flex-1 p-6 space-y-6">
				<!-- Actions Section -->
				<div class="space-y-4">
					<h3 class="text-sm font-semibold tracking-wide text-gray-800 uppercase">Actions</h3>
					<div class="space-y-3">
						<Button
							onclick={() => nodeSidebarOpen = true}
							variant="outline"
							class="justify-start w-full"
						>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
							</svg>
							Add Nodes
						</Button>
						<Button
							onclick={applyAutoLayout}
							variant="outline"
							class="justify-start w-full"
						>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
							</svg>
							Auto Layout
						</Button>
						<Button
							onclick={resetToDefaults}
							variant="outline"
							class="justify-start w-full"
						>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
							</svg>
							Reset to Defaults
						</Button>
						<Button
							onclick={exportConfig}
							class="justify-start w-full text-white bg-amq-primary hover:bg-amq-dark"
						>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
							</svg>
							Export Configuration
						</Button>
					</div>
				</div>

				<!-- Info Section -->
				<div class="space-y-4">
					<h3 class="text-sm font-semibold tracking-wide text-gray-800 uppercase">Instructions</h3>
					<div class="space-y-2 text-sm text-gray-600">
						<p>• Drag nodes to reposition them</p>
						<p>• Click on nodes to edit settings</p>
						<p>• Use controls to zoom and pan</p>
						<p>• Export when ready to use</p>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Fullscreen Editor -->
	<main class="absolute inset-0 w-full h-full">
		<div class="w-full h-full">
			{#key svelteFlowKey}
				<SvelteFlow
					bind:domNode={svelteFlowContainer}
					{nodes}
					{edges}
					{nodeTypes}
					onnodedrag={onNodeDrag}
					onnodedragstop={onNodeDragStop}
					onnodeclick={onNodeClick}
					onedgeclick={onEdgeClick}
					fitView
					proOptions={{ hideAttribution: true }}
					nodesDraggable={true}
					nodesConnectable={false}
					elementsSelectable={true}
					zoomOnDoubleClick={false}
			>
				<Background variant="dots" gap={20} size={1} color="rgba(223, 105, 117, 0.1)" />
				<Controls />
			</SvelteFlow>
		{/key}
		</div>
	</main>

	<!-- Node Sidebar -->
	<NodeSidebar bind:isOpen={nodeSidebarOpen} onAddNode={handleAddNode} existingZones={getExistingZones()} />

	<!-- Confirmation Modal -->
	<ConfirmationModal
		bind:open={confirmationModalOpen}
		title={confirmationModalData.title}
		description={confirmationModalData.description}
		onConfirm={confirmationModalData.onConfirm}
		onCancel={confirmationModalData.onCancel}
		variant="destructive"
	/>
</div>

<style>
	:global(.amq-gradient-text) {
		background: linear-gradient(135deg, #DF6975 0%, #E8899A 50%, #75B9DF 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}
</style>
