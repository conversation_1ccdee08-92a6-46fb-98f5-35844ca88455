<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	// Using native HTML radio inputs for now
	import { Label } from '$lib/components/ui/label';

	let { data } = $props();
	let selectedValue = $state(data.value || '');

	const options = data.options || [
		{ value: 'radio1', label: 'Radio Option 1' },
		{ value: 'radio2', label: 'Radio Option 2' },
		{ value: 'radio3', label: 'Radio Option 3' }
	];

	function handleValueChange(value) {
		selectedValue = value;
		data.value = value;
	}
</script>

<div class="radio-group-node min-w-64">
	<Handle type="target" position={Position.Left} />
	
	<Card class="border-2 border-red-300 bg-red-50/50">
		<CardHeader class="pb-2">
			<CardTitle class="flex items-center gap-2 text-sm font-semibold text-red-800">
				<span class="text-lg">🔘</span>
				{data.label || 'Radio Group'}
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-3">
				<div class="space-y-2">
					{#each options as option}
						<div class="flex items-center space-x-2">
							<input
								type="radio"
								id={`radio-${option.value}`}
								name="radio-group"
								value={option.value}
								bind:group={selectedValue}
								onchange={() => handleValueChange(option.value)}
								class="h-4 w-4 text-red-600 border-red-300 focus:ring-red-500/20 accent-red-600"
							/>
							<Label for={`radio-${option.value}`} class="text-sm text-red-700">
								{option.label}
							</Label>
						</div>
					{/each}
				</div>
				{#if selectedValue}
					<div class="text-xs text-red-600 bg-red-100 p-2 rounded">
						Selected: {options.find(opt => opt.value === selectedValue)?.label || selectedValue}
					</div>
				{/if}
			</div>
		</CardContent>
	</Card>
	
	<Handle type="source" position={Position.Right} />
</div>

<style>
	.radio-group-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
</style>
