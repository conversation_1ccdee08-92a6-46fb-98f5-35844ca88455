<!-- ComplexFormFields.svelte - Handles complex form field types -->
<script>
	import { Label } from '$lib/components/ui/label';
	import { Switch } from '$lib/components/ui/switch';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import RangeSlider from 'svelte-range-slider-pips';
	import SongTypesSelection from './complex/SongTypesSelection.svelte';
	import SongsAndTypesSelection from './complex/SongsAndTypesSelection.svelte';

	import SongDifficulty from './complex/SongDifficulty.svelte';
	import ScoreRange from './complex/ScoreRange.svelte';
	import Vintage from './complex/Vintage.svelte';
	import GenresTags from './complex/GenresTags.svelte';

	let {
		config,
		editedValue = $bindable(),
		isValid = $bindable(true),
		validationMessage = $bindable(''),
		validationWarning = $bindable(''),
		getNodeColor = () => '#6366f1',
		getTotalSongs = () => 20
	} = $props();

	// Note: Automatic adjustment logic removed - components now handle validation manually
</script>

{#if config.type === 'complex-songs-and-types'}
	<SongsAndTypesSelection
		bind:editedValue
		{config}
		{getNodeColor}
		bind:isValid
		bind:validationMessage
		bind:validationWarning
	/>

{:else if config.type === 'complex-song-types-selection'}
	<SongTypesSelection
		bind:editedValue
		{config}
		{getNodeColor}
		{getTotalSongs}
		bind:isValid
		bind:validationMessage
	/>

{:else if config.type === 'complex-song-difficulty'}
	<SongDifficulty
		bind:editedValue
		{config}
		{getNodeColor}
		{getTotalSongs}
		bind:isValid
		bind:validationMessage
	/>

{:else if config.type === 'complex-score-range'}
	<ScoreRange 
		bind:editedValue 
		{config} 
		{getNodeColor}
	/>

{:else if config.type === 'complex-vintage'}
	<Vintage 
		bind:editedValue 
		{config} 
		{getNodeColor}
	/>

{:else if config.type === 'complex-genres-tags'}
	<GenresTags 
		bind:editedValue 
		{config} 
		{getNodeColor}
	/>

{:else}
	<div class="p-8 text-center text-gray-500">
		<p>Complex form type "{config.type}" not yet implemented.</p>
		<p class="text-sm mt-2">This will be a placeholder until the specific form is created.</p>
	</div>
{/if}
